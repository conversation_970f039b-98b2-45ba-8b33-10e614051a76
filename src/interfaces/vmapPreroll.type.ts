import {
  OptionalTextType,
  Overwrite,
  TextType,
  Vmap,
  VMAP,
  VmapAdBreak,
  VmapAdBreakNormalized
} from 'adpod-tools';

export type PrerollVmap = Overwrite<Vmap, { 'vmap:VMAP': PrerollVMAP }>;

export type PrerollVMAP = Overwrite<VMAP & PrerollVMAPDebug, { 'vmap:AdBreak': VmapAdBreak }>;

type PrerollVMAPDebug = {
  Debug?: {
    RAWPrerollRequestURL: OptionalTextType;
    PrerollRequestURL: TextType;
    ValidationResult: TextType;
    WhatsOnStatus: TextType;
  };
};

// PREROLL NORMALIZED

export type PrerollVmapNormalized = Overwrite<Vmap, { 'vmap:VMAP': PrerollVMAPNormalized }>;

export type PrerollVMAPNormalized = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreakNormalized }>;
