import {
  Overwrite,
  Vmap,
  VMAP,
  VmapAdBreakNormalized,
  VmapAdSourceNormalized,
  Vast4Normalized,
  VASTNormalized,
  AdVast4Normalized,
  Prettify,
  AdError,
  InlineAdVast4Normalized
} from 'adpod-tools';

export type cleanVmapNormalized = Overwrite<Vmap, { 'vmap:VMAP': cleanVMAPNormalized }>;

export type cleanVMAPNormalized = Overwrite<
  VMAP,
  { 'vmap:AdBreak': cleanVmapAdBreakNormalized[] }
>;

export type cleanVmapAdBreakNormalized = Overwrite<
  VmapAdBreakNormalized,
  { 'vmap:AdSource': cleanVmapAdSourceNormalized }
>;

type cleanVmapAdSourceNormalized = Overwrite<
  VmapAdSourceNormalized,
  { 'vmap:VASTAdData': cleanVast4Normalized }
>;

export type cleanVast4Normalized = Overwrite<Vast4Normalized, { VAST: cleanVASTNormalized }>;

type cleanVASTNormalized = Overwrite<VASTNormalized, { Ad: cleanAdVast4Normalized[] }>;

export type cleanAdVast4Normalized = Overwrite<
  AdVast4Normalized,
  { _attributes: basicAttr; InLine?: cleanInLineNormalized }
>;

export type cleanInLineNormalized = Overwrite<
  InlineAdVast4Normalized,
  { Error: AdErrorNormalized[] }
>;

type basicAttr = Prettify<Pick<AdVast4Normalized['_attributes'], 'id' | 'sequence'>>;

export type AdErrorNormalized = Required<Pick<AdError, '_cdata'>>;
