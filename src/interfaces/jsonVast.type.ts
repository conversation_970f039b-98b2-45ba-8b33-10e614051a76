import { cleanVast4Normalized, cleanVmapNormalized } from './cleanVast.type';
import { AdserverAd } from './configuration.interface';
import { Vast4Normalized } from 'adpod-tools';

export type IVastJsonOperationalData = {
  vast4Json: Vast4Normalized;
  isWithReplacedAds: boolean;
  adServerResponseLog: AdserverAd[];
};

export enum TrackingScriptsEnum {
  tracking = 'tracking',
  impression = 'impression'
}

export type slotFWTrackingScriptsType = {
  adIndex: number;
  name: string;
  value: string;
  type: TrackingScriptsEnum | null;
};

export type TrackingType = {
  _attributes: { event: string };
  _cdata: string;
};

export type VastJsonPlaylistType = cleanVast4Normalized | cleanVmapNormalized;
