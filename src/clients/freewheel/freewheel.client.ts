import { Channel, LogLevel, normalizeVast4, request, xmlParser } from 'adpod-tools';
import logger from '../../libs/logging/logger';
import { URLParamsHelper } from '../../scripts/adserver/urlHelper';
import { RequestHeaders } from '../../interfaces';
import {
  FWDurationBasedVmap,
  FWDurationBasedVmapNormalized,
  IFreewheelClient
} from './freewheelClient.interface';
import { validators } from '../../EnvValidation/envalidConfig';
import { Injectable } from '@nestjs/common';
import { DeapItemType } from '../../scripts/services/deapProfiles.service';
import { RequestInit } from 'node-fetch';

@Injectable()
export class FreewheelClient implements IFreewheelClient {
  async getDurationBasedAds(
    version: string,
    channel: Channel,
    headers: RequestHeaders,
    duration: number,
    { defaultProfiles }: DeapItemType,
    uid?: string
  ): Promise<{ response: FWDurationBasedVmapNormalized | null; url: string }> {
    const ua = headers['x-device-user-agent'];

    const urlBuilder = new URLParamsHelper(validators.FW_URL, '&')
      .add('prof', '386345:DNI_IT_HbbTV_SSAI_live', true)
      .add('nw', 386345)
      .add('metr', 1031)
      .add('caid', 'ADDRESSABLE_TV_DUMMY_ASSET')
      .add('asnw', 386345)
      .add('csid', `ADDRESSABLE_TV_ITALY_${channel}`)
      .add('vprn', 99138675)
      .add('vrdu', duration)
      .addMaybe('vip', headers['x-device-ip'])
      .add('vdur', 3600)
      .add('resp', 'vmap1+vast4', true)
      .add('flag', `+scpv+emcr+amcb+slcb+aeti;_fw_vcid2=${uid}`);

    defaultProfiles.forEach((profile) => {
      urlBuilder.add(profile, true);
    });

    const url = urlBuilder
      .add('v', version)
      .add('test', 'mxf')
      .add('ch', channel)
      .add('p', 1)
      .add('wbd_ad_dur', 'na')
      .addMaybe('_fw_h_user_agent', ua)
      .add('ptgt', 'a')
      .add('mind', duration)
      .add('maxd', duration)
      .add('tpos', 0)
      .add('slau', 'Preroll Spot', true)
      .toString();

    try {
      logger('SO_FW_REQUEST', { url, headers }, LogLevel.startOver);

      const requestInit: RequestInit | undefined = ua
        ? { headers: { 'user-agent': ua, 'x-device-user-agent': ua } }
        : undefined;

      const response = await request(url, requestInit);

      const responseText = (await response.text()) as string;

      logger(
        'SO_FW_RESPONSE',
        {
          responseStatus: response.status,
          responseStatusText: response.statusText,
          responseHeaders: response.statusText,
          responseOk: response.ok,
          responseType: response.type,
          responseUrl: response.url,
          responseText
        },
        LogLevel.startOver
      );

      const jsonResult = xmlParser.fromXMLtoJSON(responseText) as FWDurationBasedVmap;

      logger('SO_FW_XML_TO_JSON', { xmlToJson: jsonResult }, LogLevel.startOver);

      return { response: this.normalizeFWDurationBasedVmap(jsonResult), url };
    } catch (err) {
      logger('ERROR_SO_FW_FETCH', { err }, LogLevel.error);
      return { response: null, url };
    }
  }

  private normalizeFWDurationBasedVmap(
    obj: FWDurationBasedVmap
  ): FWDurationBasedVmapNormalized {
    const adData = obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'];

    obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'] =
      normalizeVast4(adData);

    return obj as FWDurationBasedVmapNormalized;
  }
}
