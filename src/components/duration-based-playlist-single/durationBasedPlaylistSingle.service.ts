import { Injectable } from '@nestjs/common';
import { IPlaylistResponse, IRequestLog, RequestHeaders } from '../../interfaces';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { Channel, hmsToMilliseconds, LogLevel, xmlParser } from 'adpod-tools';
import { AdSlotBuilder } from '../../scripts/vast/adSlot.builder';
import { Protocol } from '../../models/protocol.model';
import { createEmptyVast } from '../../scripts/vast/createEmptyVast';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import {
  FWDurationBasedVmapNormalized,
  IFreewheelClient
} from '../../clients/freewheel/freewheelClient.interface';
import {
  AdPromoServerDurationBasedVmapNormalized,
  IAdPromoServerClient
} from '../../clients/adPromoServer/adPromoServerClient.interface';
import dayjs from 'dayjs';
import dayjsDuration from 'dayjs/plugin/duration';
import _ from 'lodash';
import logger from '../../libs/logging/logger';
import { DurationBasedPlaylistMode } from '../../models/playlistMode.model';
import { IDeapProfilesService } from '../../scripts/services/deapProfiles.service';
import { generatePlaylistBreakInfo } from '../../scripts/logs/generatePlaylistBreakInfo';
import { ICacheProvider } from '../../libs/caching';
import { WorkerConfigType } from '../../models/workerConfig';

dayjs.extend(dayjsDuration);

export abstract class IDurationBasedPlaylistSingleService {
  abstract handle(
    channel: Channel,
    requestMacroParams: RequestMacroParams,
    version: string,
    duration: number,
    uid?: string,
    output?: PlaylistOutputs,
    headers?: RequestHeaders,
    mode?: DurationBasedPlaylistMode
  ): Promise<IPlaylistResponse>;
}

@Injectable()
export class DurationBasedPlaylistSingleService
  implements IDurationBasedPlaylistSingleService
{
  constructor(
    private readonly localCache: ICacheProvider,
    private readonly fwClient: IFreewheelClient,
    private readonly adPromoServerClient: IAdPromoServerClient,
    private readonly deapProfilesService: IDeapProfilesService
  ) {}

  public async handle(
    channel: Channel,
    requestMacroParams: RequestMacroParams,
    version: string,
    duration: number,
    uid?: string,
    output = PlaylistOutputs.default,
    headers = {},
    mode?: DurationBasedPlaylistMode
  ): Promise<IPlaylistResponse> {
    const deapProfiles = await this.deapProfilesService.getDeapProfiles(channel, uid);

    const requestLog: IRequestLog = {
      channel,
      configVersion: version,
      mode: mode as string,
      output,
      requestMacroParams
    };

    const { response: fwNormalizedResponse, url } = await this.fwClient.getDurationBasedAds(
      version,
      channel,
      headers,
      duration,
      deapProfiles,
      uid
    );

    const fwAdSlots =
      fwNormalizedResponse?.['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData']
        .VAST.Ad;

    if (!fwNormalizedResponse || !fwAdSlots?.length) {
      const emptyVastReason = 'SO_NO_FREEWHEEL_RESPONSE';
      return {
        playlist: await createEmptyVast(requestLog, emptyVastReason, output),
        emptyVastReason,
        playlistInfo: null,
        requestLog
      };
    }

    const VmapNormalized = structuredClone(fwNormalizedResponse);

    this.addDebug(VmapNormalized, url, mode);

    this.modifyFWResponse(VmapNormalized, channel, requestMacroParams, version, duration, uid);

    const fwDurationToFill = this.countAdsTimeToFill(VmapNormalized, duration);
    const filler = await this.getFreeWheelFiller(version);

    const shouldFillAds = fwDurationToFill !== 0 && filler;
    logger('SO_SHOULD_FILL_ADS', { fwDurationToFill, filler, shouldFillAds }, LogLevel.dev);

    if (fwDurationToFill < 0)
      logger('SO_FW_DURATION_TO_FILL', { fwDurationToFill }, LogLevel.warn);

    if (shouldFillAds) {
      return this.fillWithAdPromoAds(
        channel,
        version,
        output,
        headers,
        fwDurationToFill,
        VmapNormalized,
        requestLog
      );
    }

    return {
      playlist: xmlParser.fromJSONtoXML(VmapNormalized),
      emptyVastReason: null,
      playlistInfo: generatePlaylistBreakInfo(
        VmapNormalized['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'],
        version,
        channel
      ),
      requestLog
    };
  }

  private modifyFWResponse(
    response: FWDurationBasedVmapNormalized,
    channel: Channel,
    requestMacroParams: RequestMacroParams,
    version: string,
    duration: number,
    uid?: string
  ) {
    const { adSlots, trackings } = this.extractAdSlotsAndTrackings(response);

    for (const adSlot of adSlots) {
      const { sequence: position } = adSlot._attributes;
      const dai = 'dai';
      const adTrackingBuilder = new AdSlotBuilder(adSlot);
      const creativeId = adSlot.InLine?.Creatives.Creative[0]._attributes?.id ?? '';

      adTrackingBuilder
        .addImpression(Protocol.https, {
          p: position,
          ch: channel,
          v: version,
          t: dai,
          uid,
          dur: duration,
          e: 'impression',
          bt: dai,
          cust_params: requestMacroParams.custParams
        })
        .addDefaultTrackingScripts(Protocol.https, {
          p: position,
          ch: channel,
          v: version,
          t: dai,
          dur: duration,
          uid,
          bt: dai,
          cust_params: requestMacroParams.custParams
        })
        .addBreakStartImpression(trackings)
        .addBreakEndTracking(trackings)
        .setUniversalAdIdForFirstCreative(creativeId)
        .build();
    }
  }

  private async fillWithAdPromoAds(
    channel: Channel,
    version: string,
    output: PlaylistOutputs,
    headers: RequestHeaders,
    fwDurationToFill: number,
    fwNormalizedResponse: FWDurationBasedVmapNormalized,
    requestLog: IRequestLog
  ): Promise<IPlaylistResponse> {
    const { response: adPromoNormalizedResponse } =
      await this.adPromoServerClient.getFillerAds(channel, fwDurationToFill, headers);

    if (!adPromoNormalizedResponse) {
      const emptyVastReason = 'SO_ADPROMO_NO_ADS';
      return {
        playlist: await createEmptyVast(requestLog, emptyVastReason, output),
        emptyVastReason,
        playlistInfo: null,
        requestLog
      };
    }

    const adPromoDurationToFill = this.countAdsTimeToFill(
      adPromoNormalizedResponse,
      fwDurationToFill
    );

    const adPromoDoNotHaveAdsForRequestedDuration = _.inRange(
      adPromoDurationToFill,
      1,
      fwDurationToFill
    );
    logger(
      'SO_DOES_ADPROMO_HAVE_ENOUGH_ADS',
      { adPromoDurationToFill, adPromoDoNotHaveAdsForRequestedDuration },
      LogLevel.startOver
    );

    if (adPromoDoNotHaveAdsForRequestedDuration) {
      const emptyVastReason = 'SO_ADPROMO_DOES_NOT_HAVE_ENOUGH_ADS';
      return {
        playlist: await createEmptyVast(requestLog, emptyVastReason, output),
        emptyVastReason,
        playlistInfo: null,
        requestLog
      };
    }

    const mergedVmaps = this.mergeAds(fwNormalizedResponse, adPromoNormalizedResponse);

    return {
      playlist: xmlParser.fromJSONtoXML(mergedVmaps),
      emptyVastReason: null,
      playlistInfo: generatePlaylistBreakInfo(
        mergedVmaps['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'],
        version,
        channel
      ),
      requestLog
    };
  }

  private countAdsTimeToFill(
    vmap: FWDurationBasedVmapNormalized | AdPromoServerDurationBasedVmapNormalized,
    duration: number
  ) {
    const { adSlots } = this.extractAdSlotsAndTrackings(vmap);

    const count = adSlots.reduce((acc, adSlot) => {
      const slotDuration =
        adSlot.InLine?.Creatives.Creative[0].Linear?.Duration._text ?? '00:00:00';
      const convertedDuration = dayjs
        .duration(hmsToMilliseconds(slotDuration) ?? 0, 'ms')
        .asSeconds();
      return acc + convertedDuration;
    }, 0);

    return duration - count;
  }

  private mergeAds(
    fwVamp: FWDurationBasedVmapNormalized,
    adPromoVmap: AdPromoServerDurationBasedVmapNormalized
  ) {
    const { adSlots: freeWheelAdSlots } = this.extractAdSlotsAndTrackings(fwVamp);
    const { adSlots: adPromoAdSlots } = this.extractAdSlotsAndTrackings(adPromoVmap);

    freeWheelAdSlots.push(...adPromoAdSlots);

    return fwVamp;
  }

  private extractAdSlotsAndTrackings(
    response: FWDurationBasedVmapNormalized | AdPromoServerDurationBasedVmapNormalized
  ) {
    const adSlots =
      response['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad;
    const trackings =
      response['vmap:VMAP']['vmap:AdBreak']['vmap:TrackingEvents']?.['vmap:Tracking'] ?? [];

    return { adSlots, trackings };
  }

  private async getFreeWheelFiller(version: string): Promise<boolean> {
    const workerConfig = await this.localCache.get<WorkerConfigType>('workerConfig');
    return !!workerConfig?.freeWheelDuration?.[version]?.filler;
  }

  private addDebug(
    response: FWDurationBasedVmapNormalized,
    fwRequestUrl: string,
    mode?: DurationBasedPlaylistMode
  ) {
    if (mode && mode === DurationBasedPlaylistMode.debug) {
      response['vmap:VMAP']['vmap:AdBreak'].Debug =
        response['vmap:VMAP']['vmap:AdBreak'].Debug ?? {};
      response['vmap:VMAP']['vmap:AdBreak'].Debug.AdServerDetails = { _text: fwRequestUrl };
    }
  }
}
