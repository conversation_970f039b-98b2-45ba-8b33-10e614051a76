import { Test, TestingModule } from '@nestjs/testing';
import { Channel } from 'adpod-tools';
import {
  DurationBasedPlaylistSingleService,
  IDurationBasedPlaylistSingleService
} from './durationBasedPlaylistSingle.service';
import { Protocol } from '../../models/protocol.model';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import {
  FWDurationBasedVmapNormalized,
  IFreewheelClient
} from '../../clients/freewheel/freewheelClient.interface';
import {
  AdPromoServerDurationBasedVmapNormalized,
  IAdPromoServerClient
} from '../../clients/adPromoServer/adPromoServerClient.interface';
import { createMock } from '@golevelup/ts-jest';
import { isEmptyVast } from '../../scripts/vast/isEmptyVast';
import { DurationBasedPlaylistMode } from '../../models/playlistMode.model';
import {
  IDeapProfilesService,
  DeapProfileService
} from '../../scripts/services/deapProfiles.service';
import { CacheProviderMock, TestCacheModule } from '../../libs/testing';
import { ICacheProvider } from '../../libs/caching';
import { TestRedisModule } from 'adpod-aws/dist/testing';

jest.mock('adpod-tools/dist/common/logging/logger');

describe('DurationBasedPlaylistSingleService', () => {
  let durationBasedPlaylistSingle: DurationBasedPlaylistSingleService;
  let fwClientMock: jest.Mocked<IFreewheelClient>;
  let adPromoServerClientMock: jest.Mocked<IAdPromoServerClient>;
  let cacheServiceMock: CacheProviderMock;

  beforeEach(async () => {
    fwClientMock = createMock();
    adPromoServerClientMock = createMock();

    const module: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule],
      providers: [
        {
          provide: IDurationBasedPlaylistSingleService,
          useClass: DurationBasedPlaylistSingleService
        },
        {
          provide: IFreewheelClient,
          useValue: fwClientMock
        },
        {
          provide: IAdPromoServerClient,
          useValue: adPromoServerClientMock
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        }
      ]
    }).compile();

    durationBasedPlaylistSingle = await module.resolve(IDurationBasedPlaylistSingleService);
    cacheServiceMock = module.get(ICacheProvider);
  });

  test('should return playlist from FW and Ad Promo Server', async () => {
    // arrange
    const fwResponse = prepareFwResponse();
    const adPromoResponse = prepareAdPromoResponse(30);
    fwClientMock.getDurationBasedAds.mockResolvedValueOnce({
      response: fwResponse,
      url: 'fwUrl'
    });
    adPromoServerClientMock.getFillerAds.mockResolvedValueOnce({
      response: adPromoResponse,
      url: ''
    });
    cacheServiceMock.get.mockResolvedValueOnce('');
    cacheServiceMock.get.mockResolvedValue({
      freeWheelDuration: { 'nowtilus_v2.0.0': { filler: true } }
    });

    // act
    const result = await durationBasedPlaylistSingle.handle(
      Channel.mtit,
      new RequestMacroParams(
        '77e120a8-abc9-4271-9398-4b7fb494c809',
        Protocol.https,
        undefined,
        undefined,
        Channel.mtit
      ),
      'nowtilus_v2.0.0',
      60,
      '77e120a8-abc9-4271-9398-4b7fb494c809',
      undefined,
      {
        'x-device-user-agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        'x-device-ip': '************'
      },
      DurationBasedPlaylistMode.debug
    );

    // assert
    expect(result).toBeDefined();
    expect(result.playlist).toMatchSnapshot();
  });

  test('should return empty playlist because Ad Promo does not have enough ads', async () => {
    // arrange
    const fwResponse = prepareFwResponse();
    const adPromoResponse = prepareAdPromoResponse(20);
    fwClientMock.getDurationBasedAds.mockResolvedValueOnce({ response: fwResponse, url: '' });
    adPromoServerClientMock.getFillerAds.mockResolvedValueOnce({
      response: adPromoResponse,
      url: ''
    });
    cacheServiceMock.get.mockResolvedValueOnce('');
    cacheServiceMock.get.mockResolvedValueOnce({
      freeWheelDuration: { 'nowtilus_v2.0.0': { filler: true } }
    });

    // act
    const result = await durationBasedPlaylistSingle.handle(
      Channel.mtit,
      new RequestMacroParams(
        '77e120a8-abc9-4271-9398-4b7fb494c809',
        Protocol.https,
        undefined,
        undefined,
        Channel.mtit
      ),
      'nowtilus_v2.0.0',
      60,
      '77e120a8-abc9-4271-9398-4b7fb494c809',
      undefined,
      {
        'x-device-user-agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        'x-device-ip': '************'
      }
    );

    // assert
    expect(result).toBeDefined();
    expect(isEmptyVast(result.playlist)).toEqual(true);
  });

  test('should return empty playlist because FW does not have ads', async () => {
    // arrange
    fwClientMock.getDurationBasedAds.mockResolvedValueOnce({ response: null, url: '' });
    cacheServiceMock.get.mockResolvedValueOnce('');
    cacheServiceMock.get.mockResolvedValueOnce(
      JSON.stringify({
        freeWheelDuration: { 'nowtilus_v2.0.0': { filler: true } }
      })
    );

    // act
    const result = await durationBasedPlaylistSingle.handle(
      Channel.mtit,
      new RequestMacroParams(
        '77e120a8-abc9-4271-9398-4b7fb494c809',
        Protocol.https,
        undefined,
        undefined,
        Channel.mtit
      ),
      'nowtilus_v2.0.0',
      60,
      '77e120a8-abc9-4271-9398-4b7fb494c809',
      undefined,
      {
        'x-device-user-agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
        'x-device-ip': '************'
      }
    );

    // assert
    expect(result).toBeDefined();
    expect(isEmptyVast(result.playlist)).toEqual(true);
  });

  function prepareFwResponse(): FWDurationBasedVmapNormalized {
    return {
      'vmap:VMAP': {
        _attributes: { version: '1.0', 'xmlns:vmap': 'http://www.iab.net/vmap-1.0' },
        'vmap:AdBreak': {
          _attributes: { breakId: '636196640', breakType: 'linear', timeOffset: 'start' },
          'vmap:AdSource': {
            _attributes: { allowMultipleAds: 'true', followRedirects: 'true', id: '1' },
            'vmap:VASTAdData': {
              VAST: {
                _attributes: {
                  version: '4.1',
                  xmlns: 'http://www.w3.org/2001/XMLSchema-instance',
                  'xmlns:xs': 'vast.xsd'
                },
                Ad: [
                  {
                    _attributes: {
                      id: '80885030.139644172316032',
                      sequence: 1,
                      breakId: ''
                    },
                    InLine: {
                      AdSystem: { _text: 'FreeWheel' },
                      AdTitle: {
                        _text:
                          'IT/ALDI S.R.L./ALDI Addressable TV TARGETING BASE 2024-11-04 - 2024-12-22 CPM/ADDRESSABLE TV'
                      },
                      Error: [
                        {
                          _cdata:
                            'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&iw=&uxnw=&uxss=&uxct=&et=e&cn=[ERRORCODE]'
                        }
                      ],
                      Impression: [
                        {
                          _attributes: { id: 'FWi_80885030.0' },
                          _cdata:
                            'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=defaultImpression&et=i&_cc=80885030,767234710,,,1732532364,1&tpos=0&iw=&uxnw=&uxss=&uxct=&metr=1031&init=1&vcid2=77e120a8-abc9-4271-9398-4b7fb494c809&cr='
                        },
                        {
                          _attributes: { id: 'FWi_80885030.0.1' },
                          _cdata:
                            'https://ads.stickyadstv.com/user-matching?id=185&_fw_gdpr=&_fw_gdpr_consent='
                        },
                        {
                          _attributes: { id: 'FWi_80885030.0.2' },
                          _cdata:
                            'https://ads.stickyadstv.com/auto-user-sync?_fw_gdpr=&_fw_gdpr_consent='
                        },
                        {
                          _attributes: { id: 'FWi_80885030.0.3' },
                          _cdata:
                            'https://dpm.demdex.net/ibs:dpid=796&dpuuid=77e120a8-abc9-4271-9398-4b7fb494c809&gdpr=&gdpr_consent='
                        }
                      ],
                      Creatives: {
                        Creative: [
                          {
                            _attributes: { AdID: '80885030', id: '196053714' },
                            Linear: {
                              Duration: { _text: '00:00:30' },
                              TrackingEvents: {
                                Tracking: [
                                  {
                                    _attributes: { event: 'complete' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=complete&et=i&_cc=&tpos=0&init=1&iw=&uxnw=&uxss=&uxct=&metr=1031'
                                  },
                                  {
                                    _attributes: { event: 'firstQuartile' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=firstQuartile&et=i&_cc=&tpos=0&init=1&iw=&uxnw=&uxss=&uxct=&metr=1031'
                                  },
                                  {
                                    _attributes: { event: 'midpoint' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=midPoint&et=i&_cc=&tpos=0&init=1&iw=&uxnw=&uxss=&uxct=&metr=1031'
                                  },
                                  {
                                    _attributes: { event: 'thirdQuartile' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=thirdQuartile&et=i&_cc=&tpos=0&init=1&iw=&uxnw=&uxss=&uxct=&metr=1031'
                                  },
                                  {
                                    _attributes: { event: 'mute' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_mute&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'unmute' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_un-mute&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'playerCollapse' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_collapse&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'playerExpand' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_expand&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'pause' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_pause&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'resume' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_resume&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'rewind' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_rewind&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'acceptInvitation' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_accept-invitation&et=s&_cc=&tpos=0'
                                  },
                                  {
                                    _attributes: { event: 'close' },
                                    _cdata:
                                      'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=_close&et=s&_cc=&tpos=0'
                                  }
                                ]
                              },
                              VideoClicks: {
                                ClickTracking: {
                                  _attributes: { id: 'FWc_80885030.0' },
                                  _cdata:
                                    'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=134742016&r=386345&adid=80885030&reid=767234710&arid=0&auid=&cn=defaultClick&et=c&_cc=&tpos=0&cr='
                                }
                              },
                              MediaFiles: {
                                MediaFile: [
                                  {
                                    _attributes: {
                                      delivery: 'progressive',
                                      height: 1080,
                                      type: 'application/mxf',
                                      width: 1920
                                    },
                                    _cdata: 'https://daicreatives.enhanced.live/ITA-94901.mxf'
                                  }
                                ]
                              }
                            }
                          }
                        ]
                      },
                      Extensions: {
                        Extension: [
                          {
                            _attributes: { type: 'wbdapm' },
                            SlotParameters: {
                              _cdata: `{ "breakID": "1", "breakType": "mirrored", "slotPosition": 1, "slotType": "mirrored" }`
                            }
                          },
                          {
                            _attributes: { type: 'FreeWheel' },
                            CreativeParameters: {
                              CreativeParameter: [
                                {
                                  _attributes: {
                                    creativeId: '196053714',
                                    name: 'MMS_ID',
                                    type: 'Linear'
                                  },
                                  _cdata: '11995'
                                },
                                {
                                  _attributes: {
                                    creativeId: '196053714',
                                    name: 'adobe_reporting',
                                    type: 'Linear'
                                  },
                                  _cdata:
                                    'advertiser_name:IT ALDI S.R.L.;advertiser_id:1068342;campaign_name:IT/ALDI S.R.L./ALDI Addressable TV TARGETING BASE 2024-11-04 - 2024-12-22/CPM/82,352.94;campaign_id:80885026;placement_name:IT/ALDI S.R.L./ALDI Addressable TV TARGETING BASE 2024-11-04 - 2024-12-22 CPM/ADDRESSABLE TV;placement_id:80885029;creative_name:ITA-94901;creative_id:196053714'
                                }
                              ]
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            }
          },
          'vmap:TrackingEvents': {
            'vmap:Tracking': [
              {
                _attributes: { event: 'breakStart' },
                _cdata:
                  'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=524288&cn=slotImpression&et=i&tpos=0&init=1&slid=0'
              },
              {
                _attributes: { event: 'breakEnd' },
                _cdata:
                  'https://5e529.v.fwmrm.net/ad/l/1?s=l054c&n=386345%3B386345&t=1732532364482896033&f=524288&cn=slotEnd&et=i&tpos=0&init=1&slid=0'
              }
            ]
          }
        }
      }
    };
  }

  function prepareAdPromoResponse(duration: number): AdPromoServerDurationBasedVmapNormalized {
    return {
      'vmap:VMAP': {
        _attributes: {
          'xmlns:vmap': 'http://iab.net/videosuite/vmap',
          version: '1.0',
          prefetch: '24:00:00'
        },
        'vmap:AdBreak': {
          _attributes: {
            timeOffset: '',
            breakType: '',
            breakId: ''
          },
          'vmap:AdSource': {
            _attributes: { id: 'ads', allowMultipleAds: 'true', followRedirects: 'true' },
            'vmap:VASTAdData': {
              VAST: {
                _attributes: {
                  'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
                  xmlns: 'http://www.iab.com/VAST',
                  version: '4.0'
                },
                Ad: [
                  {
                    _attributes: {
                      id: `PROMO_MOCKUP_${duration}`,
                      breakId: '',
                      sequence: 0
                    },
                    InLine: {
                      AdSystem: { _attributes: { version: '4.0' }, _text: 'Promo server' },
                      AdTitle: { _text: `PROMO_MOCKUP_${duration}` },
                      Creatives: {
                        Creative: [
                          {
                            _attributes: { id: 'PROMO_MOCK_150' },
                            UniversalAdId: {
                              _attributes: { idRegistry: 'DI-Ad-ID' },
                              _text: 'PROMO_MOCKUP_150'
                            },
                            Linear: {
                              Duration: { _text: `00:00:${duration}` },
                              TrackingEvents: {
                                Tracking: []
                              },
                              MediaFiles: {
                                MediaFile: [
                                  {
                                    _cdata:
                                      'https://daicreatives.enhanced.live/dummy_asset.mxf',
                                    _attributes: {
                                      delivery: 'progressive',
                                      type: 'application/mxf',
                                      width: 1920,
                                      height: 1080
                                    }
                                  }
                                ],
                                Mezzanine: {
                                  _cdata: 'https://daicreatives.enhanced.live/dummy_asset.mxf',
                                  _attributes: {
                                    delivery: 'progressive',
                                    type: 'application/mxf',
                                    width: 1920,
                                    height: 1080
                                  }
                                }
                              }
                            }
                          }
                        ]
                      },
                      Extensions: {
                        Extension: [
                          {
                            _attributes: { type: 'wbdapm' },
                            SlotParameters: {
                              _cdata: `{ "breakID": "1", "breakType": "mirrored", "slotPosition": 1, "slotType": "mirrored" }`
                            }
                          }
                        ]
                      },
                      Impression: [],
                      Error: []
                    }
                  }
                ]
              }
            }
          }
        }
      }
    };
  }
});
