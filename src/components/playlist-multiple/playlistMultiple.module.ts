import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { PlaylistMultipleController } from './playlistMultiple.controller';
import { PlaylistMultipleService } from './playlistMultiple.service';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import { CustomParamsGenerator } from '../../scripts/services/customParamsGenerator.service';
import { DaiAdsProviderFactory } from '../../scripts/services/daiAdsProvider/daiAdsProviderFactory';
import { AdOceanBreakDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanBreakDaiAdsProvider';
import { AdOceanHandler } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanHandler.service';
import { AdOceanProxyDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanProxyDaiAdsProvider';
import { UltimateDaiAdsProvider } from '../../scripts/services/daiAdsProvider/UltimateDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelDaiAdsProvider.service';
import { GoogleAdManagerProvider } from '../../scripts/services/daiAdsProvider/GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import {
  DeapProfileService,
  IDeapProfilesService
} from '../../scripts/services/deapProfiles.service';
import { TcfService } from '../../scripts/services/tcf.service';
import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelFillersAdsProvider.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { DebugService, IDebugService } from '../../libs/caching/services/debug.service';
import { ConfigurationService, IConfigurationService } from './services/configuration.service';
import { requestContextMiddleware } from '../../libs/middlewares/request-context.middleware';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';
import {
  AdSlotPlaylistTransformer,
  IAdSlotPlaylistTransformer
} from '../../scripts/services/playlist/adSlotPlaylistTransformer.service';
import { CleanUpTransformersService } from '../../scripts/services/playlist/cleanUpTransformers';

@Module({
  controllers: [PlaylistMultipleController],
  providers: [
    PlaylistMultipleService,
    PlaylistMerger,
    CustomParamsGenerator,
    JsonPlaylistService,
    DaiAdsProviderFactory,
    UltimateDaiAdsProvider,
    AdOceanHandler,
    GoogleAdManagerProvider,
    FreeWheelDaiAdsProvider,
    AdOceanBreakDaiAdsProvider,
    AdOceanProxyDaiAdsProvider,
    TcfService,
    GeneralPlaylistTransformer,
    CleanUpTransformersService,
    {
      provide: IAdSlotPlaylistTransformer,
      useClass: AdSlotPlaylistTransformer
    },
    {
      provide: IFreeWheelFillersAdsProviderService,
      useClass: FreeWheelFillersAdsProviderService
    },
    {
      provide: IFillerAdsService,
      useClass: FillerAdsService
    },
    {
      provide: IDeapProfilesService,
      useClass: DeapProfileService
    },
    {
      provide: IDebugService,
      useClass: DebugService
    },
    {
      provide: IConfigurationService,
      useClass: ConfigurationService
    }
  ]
})
export class PlaylistMultipleModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(requestContextMiddleware).forRoutes('*');
  }
}
