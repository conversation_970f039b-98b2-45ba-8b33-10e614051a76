import { Injectable } from '@nestjs/common';
import { LogLevel } from 'adpod-tools';
import { validators } from '../../EnvValidation/envalidConfig';
import { ICacheProvider } from '../../libs/caching';
import { AWSS3FileContent } from 'adpod-aws';
import logger from '../../libs/logging/logger';
import { WorkerConfigType } from '../../models/workerConfig';

@Injectable()
export class WorkerConfigService {
  constructor(
    private readonly localCache: ICacheProvider,
    private readonly awsS3FileContent: AWSS3FileContent
  ) {}

  async update(): Promise<void> {
    logger('WORKER_CONFIG_UPDATE_START', {}, LogLevel.dev);

    const path = validators.S3_WORKER_CONFIG_PATH;

    const response = (await this.awsS3FileContent.getFileContent(
      path
    )) as any as WorkerConfigType;

    if (response) {
      globalThis.workerConfig = {
        versionLogging: response.versionLogging,
        loggingGroups: response.loggingGroups,
        audit: response.audit
      };
      await this.localCache.set('workerConfig', response, validators.WORKER_CONFIG_TTL);
    }

    logger('WORKER_CONFIG_UPDATE_END', {}, LogLevel.dev);
  }
}
