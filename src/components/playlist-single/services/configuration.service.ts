import { Injectable } from '@nestjs/common';
import { Channel, IConfiguration, LogLevel } from 'adpod-tools';
import dayjs from 'dayjs';
import {
  BreaksConfigurationCacheService,
  TimeRange,
  WorkerConfigCacheService
} from '../../../libs';
import { validators } from '../../../EnvValidation/envalidConfig';
import logger from '../../../libs/logging/logger';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { AWSS3FileContent } from 'adpod-aws';

export abstract class IConfigurationService {
  abstract getConfiguration(
    channel: Channel,
    version: string,
    mode: PlaylistMode,
    time?: string,
    bid?: string,
    bidDate?: string
  ): Promise<IConfiguration | null>;
}

@Injectable()
export class ConfigurationService implements IConfigurationService {
  constructor(
    private readonly breaksConfigurationCacheService: BreaksConfigurationCacheService,
    private readonly workerConfigCacheService: WorkerConfigCacheService,
    private readonly awsS3FileContent: AWSS3FileContent
  ) {}

  public async getConfiguration(
    channel: Channel,
    version: string,
    mode: PlaylistMode,
    time?: string,
    bid?: string,
    bidDate?: string
  ): Promise<IConfiguration | null> {
    let configuration: IConfiguration | null = null;

    if (!bid && time) {
      logger('GET_CONFIG_BY_TIME_RANGE', undefined, LogLevel.debug);
      configuration = await this.getConfigurationByTimeRange(time, channel, version);
    }

    if (bid) {
      logger('GET_CONFIG_BY_BID', undefined, LogLevel.debug);
      configuration = await this.getConfigurationByBid(channel, version, bid, bidDate);
    }

    if (!configuration?.metadata?.hasAtvSlot && mode === PlaylistMode.default) {
      logger(
        'GET_CONFIG_NO_ATV_OR_DEFAULT',
        { hasAtvSlot: !configuration?.metadata?.hasAtvSlot, mode },
        LogLevel.debug
      );
      return null;
    }

    return configuration;
  }

  private async getConfigurationByBid(
    channel: Channel,
    version: string,
    bid: string,
    bidDate?: string
  ) {
    const { outOfRangeConfigsTTL, keepOutOfRangeConfigs } =
      await this.workerConfigCacheService.getScheduleConfigsAvailability();

    if (bidDate) {
      const bidDateRange = TimeRange.create(dayjs(bidDate), dayjs(bidDate).endOf('day'));

      const cacheConfigs = (
        await this.breaksConfigurationCacheService.getConfigsFromCache(channel, version)
      ).filter((config) => bidDateRange.isAfterStart(config.time));

      let foundConfig = cacheConfigs.find((config) => config.id === bid);
      if (foundConfig) {
        logger('GET_FOUND_CONFIG_IN_CACHE_BY_BID_DATE', { bid }, LogLevel.debug);
        return foundConfig;
      }

      const bidDateConfigs = await this.getConfigsFromBucket(bidDateRange, channel, version);

      logger(
        'GET_BID_DATE_CONFIGS_COUNT_FROM_BUCKET',
        { bidDateConfigsCount: bidDateConfigs.length, bidDateRange: bidDateRange.toFormat() },
        LogLevel.debug
      );

      foundConfig = bidDateConfigs.find((config) => config.id === bid);
      if (foundConfig) {
        await this.filterByWholeHourAndSetToCache(
          bidDateConfigs,
          foundConfig,
          keepOutOfRangeConfigs,
          outOfRangeConfigsTTL
        );
      }

      logger(
        'GET_CONFIG_BY_BID_DATE',
        { foundConfig: foundConfig ? { id: foundConfig.id, time: foundConfig.time } : null },
        LogLevel.debug
      );

      return foundConfig ?? null;
    }

    let cacheConfigs = await this.breaksConfigurationCacheService.getConfigsFromCache(
      channel,
      version
    );

    const bidConfig = cacheConfigs.find((config) => config.id === bid);
    if (bidConfig) {
      logger('GET_FOUND_CONFIG_IN_CACHE', { bid }, LogLevel.debug);
      return bidConfig;
    }

    const cacheBucketFetchDaysLimit = validators.CACHE_BUCKET_FETCH_DAYS_LIMIT;
    const pastTimeRange = TimeRange.create(
      dayjs().subtract(cacheBucketFetchDaysLimit, 'day'),
      dayjs().endOf('day')
    );

    logger('GET_PAST_TIME_RANGE', { pastTimeRange: pastTimeRange.toFormat() }, LogLevel.debug);

    cacheConfigs = await this.breaksConfigurationCacheService.getConfigsFromCache(
      channel,
      version
    );

    let foundConfig = cacheConfigs.find((config) => config.id === bid) ?? null;
    if (foundConfig) {
      logger(
        'GET_BID_CONFIG_FOUND_IN_CACHE',
        { foundConfig: foundConfig ? { id: foundConfig.id, time: foundConfig.time } : null },
        LogLevel.debug
      );
      return foundConfig;
    }

    const bidDateConfigs = await this.getConfigsFromBucket(pastTimeRange, channel, version);

    logger('GET_BID_CONFIGS', { bidDateConfigsCount: bidDateConfigs.length }, LogLevel.debug);

    foundConfig = bidDateConfigs.find((config) => config.id === bid) ?? null;

    if (foundConfig) {
      await this.filterByWholeHourAndSetToCache(
        bidDateConfigs,
        foundConfig,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    logger(
      'GET_BID_CONFIG_FOUND',
      { foundConfig: foundConfig ? { id: foundConfig.id, time: foundConfig.time } : null },
      LogLevel.debug
    );
    return foundConfig;
  }

  private async getConfigurationByTimeRange(time: string, channel: Channel, version: string) {
    const workerConfig = await this.workerConfigCacheService.getWorkerConfig();

    const playlistSingleTimeThreshold = workerConfig?.playlistSingleTimeThreshold ?? 0; //  End Result for 0 => Lack of Configs

    const thresholdTimeRange = this.getTimeRange(time, playlistSingleTimeThreshold);
    const dayTimeRange = thresholdTimeRange.createDayTimeRangeBasedOnDate('days');

    logger(
      'GET_TIME_RANGE',
      {
        thresholdTimeRange: thresholdTimeRange.toFormat(),
        dayTimeRange: dayTimeRange.toFormat()
      },
      LogLevel.debug
    );

    let configurations = await this.breaksConfigurationCacheService.getConfigsFromCache(
      channel,
      version
    );

    configurations = configurations.filter((config) =>
      thresholdTimeRange.isDateInTimeRange(config.time)
    );

    if (configurations.length > 0) {
      logger(
        'GET_CACHE_TIME_RANGE_GOT_CONFIGS_FROM_CACHE',
        { configsCount: configurations.length },
        LogLevel.debug
      );
      return configurations[0] ?? null;
    }

    const { outOfRangeConfigsTTL, keepOutOfRangeConfigs } =
      await this.workerConfigCacheService.getScheduleConfigsAvailability();

    configurations = await this.getConfigsFromBucket(thresholdTimeRange, channel, version);

    logger(
      'GET_TIME_RANGE_GOT_CONFIGS_FROM_BUCKET',
      { configsCount: configurations.length },
      LogLevel.debug
    );

    const foundConfig = configurations[0];

    if (foundConfig) {
      await this.filterByWholeHourAndSetToCache(
        configurations,
        foundConfig,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    return foundConfig ?? null;
  }

  private getTimeRange(requestParamTime: string, threshold: number) {
    const time =
      requestParamTime === 'now'
        ? dayjs().tz('Europe/Berlin')
        : dayjs(requestParamTime.replace(' ', '+'));

    const timeFrom = time.subtract(threshold, 'second');
    const timeTo = time.add(threshold, 'second');

    return TimeRange.create(timeFrom, timeTo);
  }

  private async getConfigsFromBucket(timeRange: TimeRange, channel: Channel, version: string) {
    const dates = timeRange.getAllDatesInRange('days', 'YYYYMMDD');

    logger('GET_GETTING_CONFIGS_FROM_BUCKET', { dates }, LogLevel.warn);

    const configs = await Promise.all(
      dates.map(async (date) =>
        this.awsS3FileContent.getConfigFromBucket(date, channel, version)
      )
    );

    const configsFromBucket = configs.filter((config) => !!config).flat();
    logger(
      'GET_FETCHED_CONFIGS_FROM_BUCKET',
      { configsCount: configsFromBucket.length, dates },
      LogLevel.debug
    );

    return configsFromBucket.filter((config) => timeRange.isDateInTimeRange(config.time));
  }

  private async filterByWholeHourAndSetToCache(
    configurations: IConfiguration[],
    foundConfig: IConfiguration,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number
  ) {
    const hourTimeRange = TimeRange.create(
      dayjs(foundConfig.time),
      dayjs(foundConfig.time)
    ).createDayTimeRangeBasedOnDate('hours');

    const configsFromBucketFilteredByTimeRange = configurations.filter((configuration) =>
      hourTimeRange.isDateInTimeRange(configuration.time)
    );

    await this.breaksConfigurationCacheService.setConfigToCache(
      configsFromBucketFilteredByTimeRange,
      keepOutOfRangeConfigs,
      outOfRangeConfigsTTL
    );
  }
}
