import { IConfiguration } from 'adpod-tools';
import { PlaylistSingleService } from './playlistSingle.service';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { Version } from '../../models/version.model';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { IVastJsonOperationalData } from '../../interfaces';
import { scenario1 } from '../../assets/mocks/simpleScenario1';
import { multipleAdsVast } from '../../assets/mocks/multipleAdsVast';
import { Protocol } from '../../models/protocol.model';
import { Test, TestingModule } from '@nestjs/testing';
import { PrerollService } from './services/preRoll.service';
import { WhatsonService } from '../whatson/whatson.service';
import { BreaksConfigurationCacheService } from '../../libs/caching/services/breaksConfigurationCache.service';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import { CustomParamsGenerator } from '../../scripts/services/customParamsGenerator.service';
import {
  IDeapProfilesService,
  DeapProfileService
} from '../../scripts/services/deapProfiles.service';
import { IDebugService } from '../../libs/caching/services/debug.service';
import { createMock } from '@golevelup/ts-jest';
import { TestCacheModule } from '../../libs/testing';
import { IConfigurationService } from './services/configuration.service';
import { WorkerConfigCacheService } from '../../libs';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';
import {
  AdSlotPlaylistTransformer,
  IAdSlotPlaylistTransformer
} from '../../scripts/services/playlist/adSlotPlaylistTransformer.service';
import { TestAwsModule, TestRedisModule } from 'adpod-aws/dist/testing';
import { CleanUpTransformersService } from '../../scripts/services/playlist/cleanUpTransformers';

jest.mock('adpod-tools/dist/common/logging/logger');

const emptyVast4Vmap: string =
  '<vmap:VMAP xmlns:vmap="http://iab.net/videosuite/vmap" version="1.0"><vmap:AdBreak timeOffset="HH:MM:SS" breakType="mirrored" breakId="empty"><vmap:AdSource id="ads" allowMultipleAds="true" followRedirects="true"><vmap:VASTAdData><VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST"></VAST></vmap:VASTAdData></vmap:AdSource></vmap:AdBreak></vmap:VMAP>';
const expectedVast4VmapDefault: string =
  '<vmap:VMAP xmlns:vmap="http://iab.net/videosuite/vmap" version="1.0"><vmap:AdBreak timeOffset="2020-09-24T22:30:05+02:00" breakType="mirrored" breakId="1"><vmap:AdSource id="ads" allowMultipleAds="true" followRedirects="true"><vmap:VASTAdData><VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST"><Ad id="Spot_2" sequence="1"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:15</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="thirdQuartile"/><Tracking event="complete"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Impression id=""/><Impression id=""/><Extensions><Extension type="wbdapm"><SlotParameters><![CDATA[{ "breakID": "7515464971096321", "breakType": "mirrored", "slotPosition": 1, "slotType": "mirrored" }]]></SlotParameters></Extension></Extensions></InLine></Ad><Ad id="Spot_4" sequence="1"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:30</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/9f62b8625f914a002496335037e9ad97/d43ca65a-7d4f-46e5-bfbb-3e08a5858f0a-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Extensions><Extension type="wbdapm"><SlotParameters><![CDATA[{ "breakID": "7515464971096321", "breakType": "mirrored", "slotPosition": 2, "slotType": "mirrored" }]]></SlotParameters></Extension></Extensions></InLine></Ad></VAST></vmap:VASTAdData></vmap:AdSource></vmap:AdBreak></vmap:VMAP>';
const expectedVast4Vmap: string =
  '<VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST"><Ad id="Spot_2" sequence="1"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:15</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="thirdQuartile"/><Tracking event="complete"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Impression id=""/><Impression id=""/><Extensions><Extension type="wbdapm"><SlotParameters><![CDATA[{ "breakID": "7515464971096321", "breakType": "mirrored", "slotPosition": 1, "slotType": "mirrored" }]]></SlotParameters></Extension></Extensions></InLine></Ad><Ad id="Spot_4" sequence="1"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:30</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/9f62b8625f914a002496335037e9ad97/d43ca65a-7d4f-46e5-bfbb-3e08a5858f0a-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Extensions><Extension type="wbdapm"><SlotParameters><![CDATA[{ "breakID": "7515464971096321", "breakType": "mirrored", "slotPosition": 2, "slotType": "mirrored" }]]></SlotParameters></Extension></Extensions></InLine></Ad></VAST>';

const spyJson = jest.fn();
let singlePlaylistService: PlaylistSingleService;
let debugServiceMock: jest.Mocked<IDebugService>;
let configurationServiceMock: jest.Mocked<IConfigurationService>;

describe('PlaylistSingleService', () => {
  const requestMacroParams = new RequestMacroParams('123', Protocol.http, '1');

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    debugServiceMock = createMock();
    configurationServiceMock = createMock();

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule, TestAwsModule],
      providers: [
        PlaylistSingleService,
        PrerollService,
        WhatsonService,
        BreaksConfigurationCacheService,
        WorkerConfigCacheService,
        CustomParamsGenerator,
        PlaylistMerger,
        GeneralPlaylistTransformer,
        CleanUpTransformersService,
        {
          provide: IAdSlotPlaylistTransformer,
          useClass: AdSlotPlaylistTransformer
        },
        {
          provide: JsonPlaylistService,
          useValue: { create: spyJson }
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        },
        {
          provide: IDebugService,
          useValue: debugServiceMock
        },
        {
          provide: IConfigurationService,
          useValue: configurationServiceMock
        }
      ]
    }).compile();

    singlePlaylistService = app.get(PlaylistSingleService);
  });

  test('should return empty vast when config is empty', async () => {
    configurationServiceMock.getConfiguration.mockResolvedValueOnce(null);
    const result = await singlePlaylistService.createSingleBreakVastPlaylist(
      requestMacroParams,
      Version.base,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      '1'
    );
    expect(result.playlist).toEqual(emptyVast4Vmap);
  });

  test('should return empty vast when ads were not replaced & Playlist mode = default', async () => {
    configurationServiceMock.getConfiguration.mockResolvedValueOnce(
      scenario1 as unknown as IConfiguration
    );
    spyJson.mockResolvedValue({
      isWithReplacedAds: false
    } as IVastJsonOperationalData);
    const result = await singlePlaylistService.createSingleBreakVastPlaylist(
      requestMacroParams,
      Version.base,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      '1'
    );
    expect(result.playlist).toEqual(emptyVast4Vmap);
  });

  test('should return expected vast; output = default', async () => {
    configurationServiceMock.getConfiguration.mockResolvedValueOnce(
      scenario1 as unknown as IConfiguration
    );
    spyJson.mockResolvedValue({
      isWithReplacedAds: true,
      vast4Json: multipleAdsVast,
      adServerResponseLog: []
    } as IVastJsonOperationalData);
    const result = await singlePlaylistService.createSingleBreakVastPlaylist(
      requestMacroParams,
      Version.base,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      '1'
    );
    expect(result.playlist).toEqual(expectedVast4VmapDefault);
  });

  test('should return expected vast; output = vast4', async () => {
    configurationServiceMock.getConfiguration.mockResolvedValueOnce(
      scenario1 as unknown as IConfiguration
    );
    spyJson.mockResolvedValue({
      isWithReplacedAds: true,
      vast4Json: multipleAdsVast,
      adServerResponseLog: []
    } as IVastJsonOperationalData);
    const result = await singlePlaylistService.createSingleBreakVastPlaylist(
      requestMacroParams,
      Version.base,
      undefined,
      undefined,
      undefined,
      PlaylistOutputs.vast4,
      undefined,
      undefined,
      '1'
    );
    expect(result.playlist).toEqual(expectedVast4Vmap);
  });
});
