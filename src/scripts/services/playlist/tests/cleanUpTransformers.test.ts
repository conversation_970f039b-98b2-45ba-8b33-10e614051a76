import { Test, TestingModule } from '@nestjs/testing';
import { CleanUpTransformersService } from '../cleanUpTransformers';
import { WorkerConfigCacheService } from '../../../../libs';
import { Channel, Vast4Normalized, VmapNormalized, availableItalyChannels } from 'adpod-tools';
import { WorkerConfigType } from '../../../../models/workerConfig';
import { requestContextStorage } from '../../../../libs/request-context';
import { TestCacheModule } from '../../../../libs/testing';
import { TestAwsModule } from 'adpod-aws/dist/testing';

jest.mock('adpod-tools/dist/common/logging/logger');

describe('CleanUpTransformersService test suite', () => {
  let service: CleanUpTransformersService;
  let spyGetWorkerConfig: jest.SpyInstance<Promise<WorkerConfigType | undefined>>;

  const mockWorkerConfig = {
    responseIAB: {
      include: {
        explicit: [{ channel: Channel.ttv, version: 'v1_0_0' }]
      }
    },
    audit: {
      modifiedDate: '2023-01-01T00:00:00Z'
    }
  } as WorkerConfigType;

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestAwsModule],
      providers: [CleanUpTransformersService, WorkerConfigCacheService]
    }).compile();

    service = app.get(CleanUpTransformersService);
    const workerConfigCacheServiceMock = app.get(WorkerConfigCacheService);
    spyGetWorkerConfig = jest.spyOn(workerConfigCacheServiceMock, 'getWorkerConfig');
  });

  describe('onModuleInit', () => {
    it('should call update on module init', async () => {
      spyGetWorkerConfig.mockResolvedValue(mockWorkerConfig);
      const updateSpy = jest.spyOn(service, 'update');

      await service.onModuleInit();

      expect(updateSpy).toHaveBeenCalledTimes(1);
      expect(spyGetWorkerConfig).toHaveBeenCalledTimes(1);
      expect(service['configModifiedDate']).toEqual('2023-01-01T00:00:00Z');
    });
  });

  describe('update', () => {
    it('should update combinations when config changes', async () => {
      spyGetWorkerConfig.mockResolvedValue(mockWorkerConfig);

      await service.update();

      expect(spyGetWorkerConfig).toHaveBeenCalledTimes(1);
      expect(service['configModifiedDate']).toEqual('2023-01-01T00:00:00Z');
    });

    it('should not update when config is undefined', async () => {
      spyGetWorkerConfig.mockResolvedValue(undefined as unknown as WorkerConfigType);
      const initialUpdate = jest.spyOn(service, 'update');

      await service.update();

      expect(initialUpdate).toHaveBeenCalledTimes(1);
      expect(service['configModifiedDate']).toEqual('');
    });

    it('should not update when responseIAB is missing', async () => {
      spyGetWorkerConfig.mockResolvedValue({
        audit: { modifiedDate: '2023-01-01T00:00:00Z' }
      } as WorkerConfigType);

      await service.update();

      expect(spyGetWorkerConfig).toHaveBeenCalledTimes(1);
      expect(service['configModifiedDate']).toEqual('');
    });

    it('should update when modifiedDate changes and not update when it does not change', async () => {
      spyGetWorkerConfig.mockResolvedValueOnce({
        responseIAB: {
          include: {
            explicit: [{ channel: Channel.ttv, version: 'v1_0_0' }]
          }
        },
        audit: { modifiedDate: '2023-01-01T00:00:00Z' }
      } as WorkerConfigType);

      expect(service['configModifiedDate']).toEqual('');

      // should update with new config based on modifiedDate
      await service.update();
      expect(service['configModifiedDate']).toEqual('2023-01-01T00:00:00Z');
      expect(service['combinations']['keys'].size).toEqual(1);

      // config change
      spyGetWorkerConfig.mockResolvedValueOnce({
        audit: { modifiedDate: '2023-01-01T00:00:00Z' }
      } as WorkerConfigType);

      // should not update with same modifiedDate
      await service.update();
      expect(service['configModifiedDate']).toEqual('2023-01-01T00:00:00Z');
      expect(service['combinations']['keys'].size).toEqual(1);

      expect(spyGetWorkerConfig).toHaveBeenCalledTimes(2);
    });

    it('should overwrite config when modifiedDate changes', async () => {
      spyGetWorkerConfig.mockResolvedValueOnce({
        responseIAB: {
          include: {
            explicit: [{ channel: Channel.ttv, version: 'v1_0_0' }]
          }
        },
        audit: { modifiedDate: '1' }
      } as WorkerConfigType);

      await service.update();
      expect(service['combinations'].has(Channel.ttv, 'v1_0_0')).toBe(true);
      expect(service['combinations'].has(availableItalyChannels.MTIT, 'v1_0_0')).toBe(false);

      spyGetWorkerConfig.mockResolvedValueOnce({
        responseIAB: {
          include: {
            explicit: [{ channel: availableItalyChannels.MTIT, version: 'v1_0_0' }]
          }
        },
        audit: { modifiedDate: '2' }
      } as WorkerConfigType);

      await service.update();
      expect(service['combinations'].has(Channel.ttv, 'v1_0_0')).toBe(false);
      expect(service['combinations'].has(availableItalyChannels.MTIT, 'v1_0_0')).toBe(true);
    });
  });

  describe('cleanUpVmap', () => {
    const mockVmap = {
      'vmap:VMAP': {
        _attributes: {
          'xmlns:vmap': 'http://iab.net/videosuite/vmap',
          version: '1.0'
        },
        'vmap:AdBreak': [
          {
            _attributes: {
              timeOffset: '00:00:00',
              breakType: 'linear',
              breakId: '1'
            },
            'vmap:AdSource': {
              _attributes: {
                id: 'ads',
                allowMultipleAds: 'true',
                followRedirects: 'true'
              },
              'vmap:VASTAdData': {
                VAST: {
                  _attributes: {
                    version: '4.0',
                    xmlns: 'http://www.iab.com/VAST'
                  },
                  Ad: [
                    {
                      _attributes: {
                        id: 'ad1',
                        sequence: 1
                      },
                      InLine: {
                        AdSystem: { _text: 'Test' },
                        AdTitle: { _text: 'Test Ad' },
                        Error: [
                          { id: '', _cdata: 'http://error1.com' },
                          { id: '', _cdata: 'http://error2.com' }
                        ]
                      }
                    }
                  ]
                }
              }
            }
          }
        ]
      }
    } as unknown as VmapNormalized;

    it('should return original vmap when ResponseIAB is disabled', () => {
      const result = service.cleanUpVmap(mockVmap);
      expect(result).toStrictEqual(mockVmap);
    });

    it('should clean up vmap when ResponseIAB is enabled', async () => {
      spyGetWorkerConfig.mockResolvedValue(mockWorkerConfig);
      await service.update();

      // Mock request context
      const mockStore = {
        channel: Channel.ttv,
        version: 'v1_0_0'
      };
      jest.spyOn(requestContextStorage, 'getStore').mockReturnValue(mockStore);

      const result = service.cleanUpVmap(mockVmap);

      expect(
        result['vmap:VMAP']['vmap:AdBreak'][0]['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad[0]
          .InLine?.Error
      ).toStrictEqual([{ _cdata: 'http://error1.com' }, { _cdata: 'http://error2.com' }]);

      expect(
        result['vmap:VMAP']['vmap:AdBreak'][0]['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad[0]
          ._attributes
      ).toStrictEqual({ id: 'ad1', sequence: 1 });
    });
  });

  describe('cleanUpVast4', () => {
    const mockVast4 = {
      VAST: {
        _attributes: {
          version: '4.0',
          xmlns: 'http://www.iab.com/VAST'
        },
        Ad: [
          {
            _attributes: {
              id: 'ad1',
              sequence: 1
            },
            InLine: {
              AdSystem: { _text: 'Test' },
              AdTitle: { _text: 'Test Ad' },
              Error: [
                { id: '', _cdata: 'http://error1.com' },
                { id: '', _cdata: 'http://error2.com' }
              ]
            }
          },
          {
            _attributes: {
              id: 'ad2',
              sequence: 2
            }
          }
        ]
      }
    } as Vast4Normalized;

    it('should return original vast4 when ResponseIAB is disabled', () => {
      const result = service.cleanUpVast4(mockVast4);
      expect(result).toStrictEqual(mockVast4);
    });

    it('should clean up vast4 when ResponseIAB is enabled', async () => {
      spyGetWorkerConfig.mockResolvedValue(mockWorkerConfig);
      await service.update();

      const mockStore = {
        channel: Channel.ttv,
        version: 'v1_0_0'
      };
      jest.spyOn(requestContextStorage, 'getStore').mockReturnValue(mockStore);

      const result = service.cleanUpVast4(mockVast4);

      expect(result.VAST.Ad).toHaveLength(2);
      expect(result.VAST.Ad[0]._attributes).toStrictEqual({ id: 'ad1', sequence: 1 });
      expect(result.VAST.Ad[0].InLine?.Error).toStrictEqual([
        { _cdata: 'http://error1.com' },
        { _cdata: 'http://error2.com' }
      ]);
      expect(result.VAST.Ad[1]._attributes).toStrictEqual({ id: 'ad2', sequence: 2 });
      expect(result.VAST.Ad[1].InLine).toBeUndefined();
    });
  });

  describe('isResponseIABEnabled behavior', () => {
    beforeEach(async () => {
      spyGetWorkerConfig.mockResolvedValue(mockWorkerConfig);
      await service.update();
    });

    it('should return false when no context store', () => {
      jest.spyOn(requestContextStorage, 'getStore').mockReturnValue(undefined);

      const mockVast4 = { VAST: { _attributes: {}, Ad: [] } } as unknown as Vast4Normalized;
      const result = service.cleanUpVast4(mockVast4);

      expect(result).toStrictEqual(mockVast4);
    });

    it('should return false when channel is missing', () => {
      const mockStore = { version: 'v1_0_0' };
      jest.spyOn(requestContextStorage, 'getStore').mockReturnValue(mockStore);

      const mockVast4 = { VAST: { _attributes: {}, Ad: [] } } as unknown as Vast4Normalized;
      const result = service.cleanUpVast4(mockVast4);

      expect(result).toStrictEqual(mockVast4);
    });

    it('should return false when version is missing', () => {
      const mockStore = { channel: Channel.ttv };
      jest.spyOn(requestContextStorage, 'getStore').mockReturnValue(mockStore);

      const mockVast4 = { VAST: { _attributes: {}, Ad: [] } } as unknown as Vast4Normalized;
      const result = service.cleanUpVast4(mockVast4);

      expect(result).toStrictEqual(mockVast4);
    });

    it('should work with italy channels', async () => {
      const italyConfig = {
        responseIAB: {
          include: {
            explicit: [{ channel: availableItalyChannels.MTIT, version: 'v1_0_0' }]
          }
        },
        audit: { modifiedDate: '2023-01-02T00:00:00Z' }
      } as WorkerConfigType;

      spyGetWorkerConfig.mockResolvedValue(italyConfig);
      await service.update();

      const mockStore = {
        channel: availableItalyChannels.MTIT,
        version: 'v1_0_0'
      };
      jest.spyOn(requestContextStorage, 'getStore').mockReturnValue(mockStore);

      const mockVast4 = {
        VAST: {
          _attributes: { version: '4.0' },
          Ad: [
            {
              _attributes: { id: 'ad1', sequence: 1 },
              InLine: {
                Error: [{ id: '', _cdata: 'http://error.com' }]
              }
            }
          ]
        }
      } as unknown as Vast4Normalized;

      const result = service.cleanUpVast4(mockVast4);

      expect(result.VAST.Ad[0].InLine?.Error).toStrictEqual([{ _cdata: 'http://error.com' }]);
    });
  });
});
