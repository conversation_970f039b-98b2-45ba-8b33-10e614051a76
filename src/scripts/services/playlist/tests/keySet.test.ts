import { KeySet } from '../helperKeySet';
import { Channel, availableItalyChannels } from 'adpod-tools';
import { ResponseIAB } from '../../../../models/workerConfig';

describe('KeySet test suite', () => {
  describe('basic functionality', () => {
    test('should create empty set when no config provided', () => {
      const keySet = new KeySet();
      expect(keySet.has(Channel.ttv, 'v1_0_0')).toBe(false);
    });

    test('should parse config with include explicit combinations', () => {
      const config: ResponseIAB = {
        include: {
          explicit: [
            { channel: Channel.ttv, version: 'v1_0_0' },
            { channel: Channel.tvn, version: 'v1_1_0' }
          ]
        }
      };
      const keySet = new KeySet(config);

      expect(keySet.has(Channel.ttv, 'v1_0_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_1_0')).toBe(true);

      expect(keySet.has(Channel.ttv, 'v1_1_0')).toBe(false);
      expect(keySet.has(Channel.tvn, 'v1_0_0')).toBe(false);
    });

    test('should parse config with include combination', () => {
      const config: ResponseIAB = {
        include: {
          combination: {
            channel: [Channel.ttv, Channel.tvn],
            version: ['v1_0_0', 'v1_1_0']
          }
        }
      };
      const keySet = new KeySet(config);

      expect(keySet.has(Channel.ttv, 'v1_0_0')).toBe(true);
      expect(keySet.has(Channel.ttv, 'v1_1_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_0_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_1_0')).toBe(true);
    });

    test('should handle exclude after include', () => {
      const config: ResponseIAB = {
        include: {
          combination: {
            channel: [Channel.ttv, Channel.tvn],
            version: ['v1_0_0', 'v1_1_0']
          }
        },
        exclude: {
          explicit: [{ channel: Channel.ttv, version: 'v1_0_0' }]
        }
      };
      const keySet = new KeySet(config);

      expect(keySet.has(Channel.ttv, 'v1_0_0')).toBe(false);
      expect(keySet.has(Channel.ttv, 'v1_1_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_0_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_1_0')).toBe(true);
    });

    test('should handle exclude combination', () => {
      const config: ResponseIAB = {
        include: {
          combination: {
            channel: [Channel.ttv, Channel.tvn, Channel.mtit],
            version: ['v1_0_0', 'v1_1_0']
          }
        },
        exclude: {
          combination: {
            channel: [Channel.ttv],
            version: ['v1_0_0', 'v1_1_0']
          }
        }
      };
      const keySet = new KeySet(config);

      expect(keySet.has(Channel.ttv, 'v1_0_0')).toBe(false);
      expect(keySet.has(Channel.ttv, 'v1_1_0')).toBe(false);

      expect(keySet.has(Channel.tvn, 'v1_0_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_1_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_0_0')).toBe(true);
      expect(keySet.has(Channel.mtit, 'v1_1_0')).toBe(true);
    });
  });

  describe('has method', () => {
    let keySet: KeySet;

    beforeEach(() => {
      const config: ResponseIAB = {
        include: {
          explicit: [
            { channel: Channel.ttv, version: 'v1_0_0' },
            { channel: availableItalyChannels.MTIT, version: 'v2_0_0' }
          ],
          combination: {
            channel: [Channel.tvn],
            version: ['v1_0_0', 'v1_1_0']
          }
        }
      };
      keySet = new KeySet(config);
    });

    test('should return true for explicit included combinations', () => {
      expect(keySet.has(Channel.ttv, 'v1_0_0')).toBe(true);
      expect(keySet.has(availableItalyChannels.MTIT, 'v2_0_0')).toBe(true);
    });

    test('should return true for combination included combinations', () => {
      expect(keySet.has(Channel.tvn, 'v1_0_0')).toBe(true);
      expect(keySet.has(Channel.tvn, 'v1_1_0')).toBe(true);
    });

    test.each`
      channel                        | version
      ${Channel.ttv}                 | ${'v1_1_0'}
      ${Channel.mtit}                | ${'v1_0_0'}
      ${availableItalyChannels.MTIT} | ${'v1_0_0'}
    `(
      `should return false for excluded combinations $channel $version`,
      ({ channel, version }) => {
        expect(keySet.has(channel, version)).toBe(false);
      }
    );

    test('should handle italy channels', () => {
      const config: ResponseIAB = {
        include: {
          explicit: [
            { channel: availableItalyChannels.MTIT, version: 'v1_0_0' },
            { channel: availableItalyChannels.TLTU, version: 'v1_1_0' }
          ]
        }
      };
      const italyKeySet = new KeySet(config);

      expect(italyKeySet.has(availableItalyChannels.MTIT, 'v1_0_0')).toBe(true);
      expect(italyKeySet.has(availableItalyChannels.TLTU, 'v1_1_0')).toBe(true);
      expect(italyKeySet.has(availableItalyChannels.MTIT, 'v1_1_0')).toBe(false);
    });
  });

  describe('edge cases', () => {
    test('should handle empty include and exclude objects', () => {
      const config: ResponseIAB = {
        include: {},
        exclude: {}
      };
      const keySet = new KeySet(config);
      expect(keySet['keys'].size).toBe(0);
    });

    test('should handle config with only exclude', () => {
      const config: ResponseIAB = {
        exclude: {
          explicit: [{ channel: Channel.ttv, version: 'v1_0_0' }]
        }
      };
      const keySet = new KeySet(config);
      expect(keySet['keys'].size).toBe(0);
    });

    test('should handle empty explicit arrays', () => {
      const config: ResponseIAB = {
        include: {
          explicit: []
        }
      };
      const keySet = new KeySet(config);
      expect(keySet['keys'].size).toBe(0);
    });

    test('should throw error on undefined combination properties', () => {
      const config: ResponseIAB = {
        include: {
          combination: {
            channel: [Channel.ttv],
            version: undefined as any
          }
        }
      };

      expect(() => new KeySet(config)).toThrow();
    });
  });
});
