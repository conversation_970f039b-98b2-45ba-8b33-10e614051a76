import { availableItalyChannels, Channel } from 'adpod-tools';
import {
  EnabledConfig,
  ResponseIAB,
  CombinationConfigType
} from '../../../models/workerConfig';

type ResponseIABKey = `${EnabledConfig['channel']}__${EnabledConfig['version']}`;

/**
 * Helper class to work with ResponseIAB config.
 * It does not validate the config.
 * Basic functionality:
 *  - check if combination of channel and version is enabled
 *  - first add all explicit and combination values from include
 *  - then remove all explicit and combination values from exclude
 */
export class KeySet {
  private keys: Set<ResponseIABKey>;

  constructor(config?: ResponseIAB) {
    this.keys = config ? this.parseConfig(config) : new Set();
  }

  public has(channel: Channel | availableItalyChannels, version: string) {
    const key = this.makeKey(channel, version);
    return this.keys.has(key);
  }

  private parseConfig(config: ResponseIAB): Set<ResponseIABKey> {
    const result = new Set<ResponseIABKey>();

    const { include, exclude } = config;

    if (include) {
      const allowedCombinations = this.parseConfigOptions(include);

      for (const combination of allowedCombinations) {
        result.add(combination);
      }
    }

    if (exclude) {
      const deniedCombinations = this.parseConfigOptions(exclude);

      for (const combination of deniedCombinations) {
        result.delete(combination);
      }
    }

    return result;
  }

  private parseConfigOptions(config: CombinationConfigType): ResponseIABKey[] {
    const { explicit, combination } = config;

    const explicitCombinations =
      explicit?.map(({ channel, version }) => this.makeKey(channel, version)) ?? [];

    const anyCombinations =
      combination?.channel.flatMap((ch) => {
        return combination?.version.map((v) => this.makeKey(ch, v));
      }) ?? [];

    return [...explicitCombinations, ...anyCombinations];
  }

  private makeKey(channel: Channel | availableItalyChannels, version: string): ResponseIABKey {
    return `${channel}__${version}`;
  }
}
