import {
  VmapNormalized,
  VmapAdBreakNormalized,
  Vast4Normalized,
  AdVast4Normalized,
  AdErrorNormalized,
  availableItalyChannels,
  Channel
} from 'adpod-tools';
import {
  cleanVmapNormalized,
  cleanVmapAdBreakNormalized,
  cleanVast4Normalized,
  cleanAdVast4Normalized
} from '../../../interfaces';
import { Injectable } from '@nestjs/common';
import { WorkerConfigCacheService } from '../../../libs';
import { requestContextStorage } from '../../../libs/request-context';
import { Interval } from '@nestjs/schedule';
import { validators } from '../../../EnvValidation/envalidConfig';
import { KeySet } from './helperKeySet';

@Injectable()
export class CleanUpTransformersService {
  private combinations: KeySet;

  private configModifiedDate: string;

  constructor(private readonly workerConfigCacheService: WorkerConfigCacheService) {
    this.configModifiedDate = '';
    this.combinations = new KeySet();
  }

  async onModuleInit() {
    await this.update();
  }

  @Interval(validators.WORKER_CONFIG_INTERVAL_UPDATE_SECONDS)
  async update(): Promise<void> {
    const config = await this.workerConfigCacheService.getWorkerConfig();

    if (!config?.responseIAB || this.configModifiedDate === config.audit.modifiedDate) {
      return;
    }

    this.configModifiedDate = config.audit.modifiedDate;
    this.combinations = new KeySet(config.responseIAB);
  }

  private isResponseIABEnabled(): boolean {
    const store = requestContextStorage.getStore();
    const channel = store?.channel as Channel | availableItalyChannels | undefined;
    const version = store?.version;

    if (!channel || !version) {
      return false;
    }

    return this.combinations.has(channel, version);
  }

  public cleanUpVmap(ad: VmapNormalized): cleanVmapNormalized {
    if (!this.isResponseIABEnabled()) {
      return ad as cleanVmapNormalized;
    }

    const cleanedAdBreaks: cleanVmapAdBreakNormalized[] = [];
    for (const adBreak of ad['vmap:VMAP']['vmap:AdBreak']) {
      cleanedAdBreaks.push(this.cleanUpAdBreak(adBreak));
    }
    return {
      'vmap:VMAP': {
        _attributes: ad['vmap:VMAP']._attributes,
        'vmap:AdBreak': cleanedAdBreaks
      }
    };
  }

  private cleanUpAdBreak(adBreak: VmapAdBreakNormalized): cleanVmapAdBreakNormalized {
    return {
      ...adBreak,
      'vmap:AdSource': {
        _attributes: adBreak['vmap:AdSource']._attributes,
        'vmap:VASTAdData': this.cleanUpVast4(adBreak['vmap:AdSource']['vmap:VASTAdData'])
      }
    };
  }

  public cleanUpVast4(VASTAdData: Vast4Normalized): cleanVast4Normalized {
    if (!this.isResponseIABEnabled()) {
      return VASTAdData as cleanVast4Normalized;
    }

    const cleanedAds: cleanAdVast4Normalized[] = [];
    for (const adVast of VASTAdData.VAST.Ad) {
      cleanedAds.push(this.cleanUpAdVast(adVast));
    }
    return {
      VAST: {
        _attributes: VASTAdData.VAST._attributes,
        Ad: cleanedAds
      }
    };
  }

  private cleanUpAdVast(adVast: AdVast4Normalized): cleanAdVast4Normalized {
    const { id, sequence } = adVast._attributes;

    if (!adVast.InLine) {
      return {
        _attributes: { id, sequence }
      };
    }

    const cleanedErrors: AdErrorNormalized[] = [];
    if (adVast.InLine?.Error) {
      for (const error of adVast.InLine.Error) {
        cleanedErrors.push({ _cdata: error._cdata });
      }
    }

    return {
      _attributes: { id, sequence },
      InLine: {
        ...adVast.InLine,
        Error: cleanedErrors
      }
    };
  }
}
