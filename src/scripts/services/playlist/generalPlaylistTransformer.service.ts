import { Injectable } from '@nestjs/common';
import {
  AdVast4Normalized,
  IConfiguration,
  returnAsArrayEmpty,
  VmapAdBreakNormalized,
  Vast4Normalized,
  LogLevel
} from 'adpod-tools';
import { cloneDeepWith, cloneDeep } from 'lodash';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { IDebugService } from '../../../libs';
import { PrefetchType } from '../../../models/prefetch.model';
import logger from '../../../libs/logging/logger';
import { cleanVmapNormalized, cleanVast4Normalized } from '../../../interfaces';
import { CleanUpTransformersService } from './cleanUpTransformers';

@Injectable()
export class GeneralPlaylistTransformer {
  constructor(
    private readonly debugService: IDebugService,
    private readonly cleanUpTransformersService: CleanUpTransformersService
  ) {}

  public updateBreakType(
    isWithReplacedAds: boolean,
    playlistJson: AdVast4Normalized[]
  ): AdVast4Normalized[] {
    // eslint-disable-next-line consistent-return
    return cloneDeepWith(playlistJson, (value, index) => {
      if (index === '_attributes' && value?.breakId) {
        return {
          ...value,
          breakType: isWithReplacedAds ? 'dai' : 'mirrored'
        };
      }
    });
  }

  public async convertToVmap(
    vastJsons: Vast4Normalized[],
    configurations: IConfiguration[] = [],
    mode?: PlaylistMode,
    bids?: string[]
  ): Promise<cleanVmapNormalized> {
    const adBreaks: VmapAdBreakNormalized[] = [];
    const debugMode = mode === PlaylistMode.debug;

    for (const [index, vastJson] of vastJsons.entries()) {
      const configuration = configurations[index];
      const bidId = configuration?.id || bids?.[index] || 'empty';

      const adBreak: VmapAdBreakNormalized = {
        _attributes: {
          timeOffset: configuration?.time || 'HH:MM:SS',
          breakType: vastJson.VAST.Ad.every((ad) => ad._attributes.linear === 'true')
            ? 'mirrored'
            : 'dai',
          breakId: bidId
        },
        'vmap:AdSource': {
          _attributes: {
            id: 'ads',
            allowMultipleAds: 'true',
            followRedirects: 'true'
          },
          'vmap:VASTAdData': {
            ...cloneDeep(vastJson)
          }
        }
      };

      if (debugMode) {
        adBreak.Debug = {
          _attributes: { breakId: bidId },
          AdRequestConnector: { _text: configuration?.breakConnector },
          Metadata: { _text: JSON.stringify(configuration?.metadata) },
          AdServerDetails: {
            _text: await this.debugService.getDebugDetails(vastJson, bidId)
          }
        };
      }

      adBreaks.push(adBreak);
    }

    const vmap = {
      'vmap:VMAP': {
        _attributes: {
          'xmlns:vmap': 'http://iab.net/videosuite/vmap',
          version: '1.0'
        },
        'vmap:AdBreak': adBreaks
      }
    };
    return this.cleanUpTransformersService.cleanUpVmap(vmap);
  }

  public convertToVAST4(
    vastJsons: Vast4Normalized[],
    prefetch?: string
  ): cleanVast4Normalized {
    const vast4 = {
      VAST: {
        _attributes: {
          ...vastJsons[0].VAST._attributes,
          prefetch
        },
        Ad: vastJsons.flatMap((v) => returnAsArrayEmpty(v.VAST.Ad))
      }
    };

    return this.cleanUpTransformersService.cleanUpVast4(vast4);
  }

  public replaceNotAllowedCharsInXml(
    playlist: string,
    mode: PrefetchType | PlaylistMode
  ): string {
    return mode === PrefetchType.nextDebugValid || mode === PlaylistMode.debugValid
      ? playlist.replaceAll('&', '&amp;')
      : playlist;
  }

  public injectPrefetchTimeToVmap = (
    vmap: cleanVmapNormalized,
    prefetchTime: string | undefined
  ): cleanVmapNormalized => {
    if (!prefetchTime) {
      logger('REJECTED_INJECTING_PREFETCH_TIME_VMAP', {}, LogLevel.dev);
      return vmap;
    }

    const vmapCopy: cleanVmapNormalized = cloneDeep(vmap);

    // Vmap
    if (vmapCopy?.['vmap:VMAP']?._attributes) {
      vmapCopy['vmap:VMAP']._attributes.prefetch = prefetchTime;
      logger('INJECTED_PREFETCH_TIME_VMAP', {}, LogLevel.dev);
    }

    return vmapCopy;
  };
}
