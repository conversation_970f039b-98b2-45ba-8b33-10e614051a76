import { availableItalyChannels } from 'adpod-tools';
import { PlaylistMode } from '../../../../models/playlistMode.model';
import { PlaylistOutputs } from '../../../../models/playlistOutput.model';
import { Test, TestingModule } from '@nestjs/testing';
import { JsonPlaylistService } from '../../createJsonPlaylist.service';
import { CustomParamsGenerator } from '../../customParamsGenerator.service';
import { AdserverAd } from '../../../../interfaces';
import { DaiAdsProviderFactory } from '../../daiAdsProvider/daiAdsProviderFactory';
import { UltimateDaiAdsProvider } from '../../daiAdsProvider/UltimateDaiAdsProvider.service';
import { AdOceanHandler } from '../../daiAdsProvider/AdOcean/AdOceanHandler.service';
import { GoogleAdManagerProvider } from '../../daiAdsProvider/GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from '../../daiAdsProvider/FreeWheel/freeWheelDaiAdsProvider.service';
import { AdOceanBreakDaiAdsProvider } from '../../daiAdsProvider/AdOcean/AdOceanBreakDaiAdsProvider';
import { AdOceanProxyDaiAdsProvider } from '../../daiAdsProvider/AdOcean/AdOceanProxyDaiAdsProvider';
import { RequestMacroParams } from '../../../configuration/injectReqParams/requestMacroParams';
import { Protocol } from '../../../../models/protocol.model';
import { DeapProfileService, IDeapProfilesService } from '../../deapProfiles.service';
import { TcfService } from '../../tcf.service';
import { IFreeWheelFillersAdsProviderService } from '../../daiAdsProvider/FreeWheel/freeWheelFillersAdsProvider.service';
import { IFillerAdsService } from 'adpod-aws';
import { freeWheelBreakConfiguration2slots as fwBreak } from './mocks/freeWheelBreakConfiguration2slots';
import daiAdsProviderFreeWheel20sResponse from './mocks/daiAdsProviderFreeWheel20sResponse.json';
import daiAdsProviderFreeWheel10sResponse from './mocks/daiAdsProviderFreeWheel10sResponse.json';
import daiAdsProviderFreeWheelDouble10sResponse from './mocks/daiAdsProviderFreeWheelDouble10sResponse.json';
import { createMock } from '@golevelup/ts-jest';
import { FillerSlotModelType } from '../../../../models/filler.model';
import { IDebugService, WorkerConfigCacheService } from '../../../../libs/caching';
import { TestCacheModule } from '../../../../libs/testing';
import { GeneralPlaylistTransformer } from '../../playlist/generalPlaylistTransformer.service';
import {
  AdSlotPlaylistTransformer,
  IAdSlotPlaylistTransformer
} from '../../playlist/adSlotPlaylistTransformer.service';
import { TestAwsModule, TestRedisModule } from 'adpod-aws/dist/testing';
import { CleanUpTransformersService } from '../../playlist/cleanUpTransformers';
import { WorkerConfigType } from '../../../../models/workerConfig';

describe('createJsonPlaylist test suite', () => {
  const getDaiAdsSpy = jest.fn();
  const isImplementedProviderSpy = jest.fn();

  let jsonPlaylistService: JsonPlaylistService;

  let freeWheelFillersAdsProviderServiceMock: jest.Mocked<IFreeWheelFillersAdsProviderService>;
  let awsFillerAdsService: jest.Mocked<IFillerAdsService>;
  let debugServiceMock: jest.Mocked<IDebugService>;
  let spyGetWorkerConfig: jest.SpyInstance<Promise<WorkerConfigType | undefined>>;

  beforeEach(async () => {
    freeWheelFillersAdsProviderServiceMock = createMock();
    awsFillerAdsService = createMock();
    debugServiceMock = createMock();

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule, TestAwsModule],
      providers: [
        JsonPlaylistService,
        CustomParamsGenerator,
        DaiAdsProviderFactory,
        AdOceanHandler,
        GoogleAdManagerProvider,
        FreeWheelDaiAdsProvider,
        AdOceanBreakDaiAdsProvider,
        AdOceanProxyDaiAdsProvider,
        TcfService,
        GeneralPlaylistTransformer,
        CleanUpTransformersService,
        WorkerConfigCacheService,
        {
          provide: IAdSlotPlaylistTransformer,
          useClass: AdSlotPlaylistTransformer
        },
        {
          provide: IFreeWheelFillersAdsProviderService,
          useValue: freeWheelFillersAdsProviderServiceMock
        },
        {
          provide: IFillerAdsService,
          useValue: awsFillerAdsService
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        },

        {
          provide: IDebugService,
          useValue: debugServiceMock
        },
        {
          provide: UltimateDaiAdsProvider,
          useValue: {
            getDaiAds: getDaiAdsSpy,
            isImplementedProvider: isImplementedProviderSpy
          }
        }
      ]
    }).compile();

    jsonPlaylistService = await app.get(JsonPlaylistService);
    const workerConfigCacheServiceMock = app.get(WorkerConfigCacheService);
    spyGetWorkerConfig = jest.spyOn(workerConfigCacheServiceMock, 'getWorkerConfig');
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllMocks();
  });

  const channel = availableItalyChannels.MTIT;
  const version = 'v1_0_0_dai_FW';
  const reqMacroParams = new RequestMacroParams(undefined, Protocol.http);
  const custParams = '';
  const playlistOutput = PlaylistOutputs.default;
  const ip = '************';
  const ua =
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36';
  const headers = {
    'x-device-ip': ip,
    'x-device-user-agent': ua
  };

  const firstMirroredAdId = 'ITA-95882';
  const secondMirroredAdId = 'ITA-91991';
  const mirroredAdIds = [firstMirroredAdId, secondMirroredAdId];
  const freeWheel10sAdId = '68991350.140543384518272';
  const freeWheel20sAdId = '68991350.140451848691456';

  describe('playList mode mirrored, without fillers', () => {
    test('should replace mirrored slots slot with FW ad with correct duration', async () => {
      getDaiAdsSpy.mockResolvedValue(daiAdsProviderFreeWheel20sResponse);
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(false);

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.mirrored,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeFalsy();
      expect(res.vast4Json.VAST.Ad.map((ad) => ad._attributes.id)).toEqual(
        expect.arrayContaining(mirroredAdIds)
      );
    });

    test('should not replace ATV slot with FW ad with incorrect duration', async () => {
      getDaiAdsSpy.mockResolvedValue(daiAdsProviderFreeWheel10sResponse);
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(false);

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.mirrored,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeFalsy();
      expect(res.vast4Json.VAST.Ad.map((ad) => ad._attributes.id)).toEqual(
        expect.arrayContaining(mirroredAdIds)
      );
    });
  });

  describe('playList mode default, without filler', () => {
    test('should replace ATV slot with FW ad with correct duration', async () => {
      getDaiAdsSpy.mockResolvedValue(daiAdsProviderFreeWheel20sResponse);
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(false);

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.default,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeTruthy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(freeWheel20sAdId);
    });

    test('should not replace ATV slot with FW ad with incorrect duration', async () => {
      getDaiAdsSpy.mockResolvedValue(daiAdsProviderFreeWheel10sResponse);
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(false);

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.default,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeFalsy();
      expect(res.vast4Json.VAST.Ad.map((ad) => ad._attributes.id)).toEqual(
        expect.arrayContaining(mirroredAdIds)
      );
    });
  });

  describe('playList mode mixed, without filler', () => {
    test('should replace ATV slot with FW ad with correct duration', async () => {
      getDaiAdsSpy.mockResolvedValue(daiAdsProviderFreeWheel20sResponse);
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(false);

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.mixed,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeTruthy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(freeWheel20sAdId);
    });

    test('should not replace ATV slot with FW ad with incorrect duration', async () => {
      getDaiAdsSpy.mockResolvedValue(daiAdsProviderFreeWheel10sResponse);
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(false);

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.mixed,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeFalsy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(secondMirroredAdId);
    });
  });

  describe('playlist with filler ads enabled', () => {
    test('should create mirrored ad slot if there is lack of FW ads and fillers ads', async () => {
      getDaiAdsSpy.mockResolvedValue(daiAdsProviderFreeWheel10sResponse);
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.getSufficientDurationAds.mockResolvedValueOnce({
        freewheelAds: [],
        fillersAdsTemplates: []
      });

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.mirrored,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeFalsy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(secondMirroredAdId);
    });

    test('should create mirrored ad if filler cannot fill ad slot', async () => {
      getDaiAdsSpy.mockResolvedValue(
        daiAdsProviderFreeWheel10sResponse as unknown as AdserverAd[]
      );
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.getSufficientDurationAds.mockResolvedValueOnce({
        freewheelAds: [],
        fillersAdsTemplates: []
      });
      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.mirrored,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeFalsy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(secondMirroredAdId);
    });

    test('should use mirrored ad if there is lack of filler ad', async () => {
      getDaiAdsSpy.mockResolvedValue(
        daiAdsProviderFreeWheel10sResponse as unknown as AdserverAd[]
      );
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.getSufficientDurationAds.mockResolvedValueOnce({
        freewheelAds: [],
        fillersAdsTemplates: []
      });
      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.default,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeFalsy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(secondMirroredAdId);
    });

    test('should replace ATV slot with FW ad with insufficient duration and add single filler ad', async () => {
      getDaiAdsSpy.mockResolvedValue(
        daiAdsProviderFreeWheel10sResponse as unknown as AdserverAd[]
      );
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.getSufficientDurationAds.mockResolvedValueOnce({
        freewheelAds: daiAdsProviderFreeWheel10sResponse[0].vast,
        fillersAdsTemplates: [{ _attributes: { id: '123.mxf', breakId: '123', sequence: 1 } }]
      } as unknown as FillerSlotModelType);

      awsFillerAdsService.getFillersAds.mockResolvedValueOnce({
        complete: true,
        totalDuration: 10,
        ads: [{ fid: '123.mxf', url: 'http://foo.com', duration: 10 }]
      });
      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.default,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeTruthy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(freeWheel10sAdId);
      expect(res.vast4Json.VAST.Ad[2]._attributes.id).toEqual('123.mxf');
    });

    test('should replace ATV slot with two FW ads', async () => {
      getDaiAdsSpy.mockResolvedValue(
        daiAdsProviderFreeWheelDouble10sResponse as unknown as AdserverAd[]
      );
      isImplementedProviderSpy.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.isPossibleFwFillerSlot.mockReturnValue(true);
      freeWheelFillersAdsProviderServiceMock.getSufficientDurationAds.mockResolvedValueOnce({
        freewheelAds: daiAdsProviderFreeWheelDouble10sResponse[0].vast,
        fillersAdsTemplates: []
      } as unknown as FillerSlotModelType);
      awsFillerAdsService.getFillersAds.mockResolvedValueOnce({
        complete: false,
        totalDuration: 0,
        ads: []
      });

      const res = await jsonPlaylistService.create(
        fwBreak,
        PlaylistMode.default,
        channel,
        version,
        reqMacroParams,
        custParams,
        playlistOutput,
        headers,
        ip,
        ua
      );

      expect(res.isWithReplacedAds).toBeTruthy();
      expect(res.vast4Json.VAST.Ad[0]._attributes.id).toEqual(firstMirroredAdId);
      expect(res.vast4Json.VAST.Ad[1]._attributes.id).toEqual(freeWheel10sAdId);
      expect(res.vast4Json.VAST.Ad[2]._attributes.id).toEqual(`${freeWheel10sAdId}_2`);
    });
  });
});
