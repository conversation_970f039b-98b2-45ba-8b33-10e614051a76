import { LogLevel } from 'adpod-tools';
import logger from '../../libs/logging/logger';
import { validators } from '../../EnvValidation/envalidConfig';
import { EnabledConfig } from '../../models/workerConfig';

export const sendRequestLogsToSnowflake = (
  sessionId: string,
  v: string,
  ch: string,
  snowFlakeEnabledConfigs: EnabledConfig[],
  reqUrl?: string,
  uid?: string,
  ip?: string
) => {
  const isLoggingEnabledForConfig = snowFlakeEnabledConfigs.find(
    (config) => config.version === v && config.channel === ch
  );
  if (!isLoggingEnabledForConfig) {
    return;
  }

  logger(
    'SNOWFLAKE_REQUEST_OK_INPUT_RAW',
    {
      input: {
        env: validators.NODE_ENV,
        ti: 'pl',
        et: 'apm_logger',
        eid: 'apm_request',
        ev: '1_0_0',
        ed: {
          session_id: sessionId,
          v,
          ch,
          url: reqUrl,
          ip
        },
        deuid: uid,
        ts: Date.now(),
        ctx: {},
        appVersion: process.env.npm_package_version
      }
    },
    LogLevel.snowflake
  );
};
