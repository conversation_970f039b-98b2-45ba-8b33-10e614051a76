import { AdType, BreakConnector, Channel, IConfiguration, OrderType } from 'adpod-tools';
import { injectReqParams } from './injectReqParams';
import { scenario4 } from '../../../assets/mocks/TEST/v1_0_0/scenario4';
import reqParams01 from '../../../assets/mocks/reqParams01.json';
import { Protocol } from '../../../models/protocol.model';
import { RequestHeaders } from '../../../interfaces';
import { RequestMacroParams } from './requestMacroParams';

describe('injectReqParams test suite', () => {
  const uid: IConfiguration = {
    adslot: [
      {
        adrequest: '',
        duration: 15,
        position: 1,
        type: AdType.tv,
        connector: BreakConnector.adoceanSlotSchedule,
        vastmirroredadsJson: {
          VAST: {
            _attributes: {
              version: '2.0',
              'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
              xmlns: 'http://www.iab.com/VAST'
            },
            Ad: [
              {
                _attributes: {
                  id: 'Spot_1',
                  sequence: 1,
                  breakId: '123'
                },
                InLine: {
                  AdSystem: { _text: 'TVN' },
                  AdTitle: { _text: 'TVN Video Ad' },
                  Creatives: {
                    Creative: [
                      {
                        Linear: {
                          Duration: { _text: '00:00:15' },
                          MediaFiles: {
                            MediaFile: [
                              {
                                _attributes: {
                                  delivery: 'progressive',
                                  height: 404,
                                  type: 'video/mp4',
                                  width: 720
                                },
                                _cdata:
                                  'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4'
                              }
                            ],
                            Mezzanine: {
                              _attributes: {
                                delivery: 'progressive',
                                height: 720,
                                type: 'video/mp4',
                                width: 1280
                              },
                              _cdata:
                                'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218.mov'
                            }
                          },
                          TrackingEvents: {
                            Tracking: [
                              {
                                _attributes: { event: 'start' },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: { event: 'firstQuartile' },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: { event: 'midpoint' },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid'
                              }
                            ]
                          }
                        }
                      }
                    ]
                  },
                  Impression: [],
                  Error: [],
                  Extensions: {
                    Extension: []
                  }
                }
              }
            ]
          }
        },
        metadata: {
          breakId: '123',
          adId: '',
          orderId: '',
          orderType: OrderType.empty
        }
      }
    ],
    duration: 15,
    id: '04',
    channel: Channel.ttv,
    version: 'v1_0_0',
    time: '2020-09-29T22:30:05+02:00'
  };

  const emptyUid: IConfiguration = {
    adslot: [
      {
        adrequest: '',
        duration: 15,
        position: 1,
        type: AdType.tv,
        connector: BreakConnector.adoceanSlotSchedule,
        metadata: {
          breakId: '123',
          adId: '',
          orderId: '',
          orderType: OrderType.empty
        },
        vastmirroredadsJson: {
          VAST: {
            _attributes: {
              version: '2.0',
              'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
              xmlns: 'http://www.iab.com/VAST'
            },
            Ad: [
              {
                _attributes: {
                  id: 'Spot_1',
                  sequence: 1,
                  breakId: '123'
                },
                InLine: {
                  AdSystem: { _text: 'TVN' },
                  AdTitle: { _text: 'TVN Video Ad' },
                  Creatives: {
                    Creative: [
                      {
                        Linear: {
                          Duration: { _text: '00:00:15' },
                          MediaFiles: {
                            MediaFile: [
                              {
                                _attributes: {
                                  delivery: 'progressive',
                                  height: 404,
                                  type: 'video/mp4',
                                  width: 720
                                },
                                _cdata:
                                  'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4'
                              }
                            ],
                            Mezzanine: {
                              _attributes: {
                                delivery: 'progressive',
                                height: 720,
                                type: 'video/mp4',
                                width: 1280
                              },
                              _cdata:
                                'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218.mov'
                            }
                          },
                          TrackingEvents: {
                            Tracking: [
                              {
                                _attributes: { event: 'start' },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: { event: 'firstQuartile' },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: { event: 'midpoint' },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid'
                              }
                            ]
                          }
                        }
                      }
                    ]
                  },
                  Impression: [],
                  Error: [],
                  Extensions: {
                    Extension: []
                  }
                }
              }
            ]
          }
        }
      }
    ],
    duration: 15,
    id: '04',
    channel: Channel.ttv,
    version: 'v1_0_0',
    time: '2020-09-29T22:30:05+02:00'
  };

  const allEmptyParams = {
    adslot: [
      {
        adrequest:
          'https://tvn.adocean.pl/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15/mid4dur=30/uid=',
        duration: 15,
        position: 1,
        type: 'ATV',
        connector: 'ADOCEAN_PROXY',
        metadata: {
          breakId: '123',
          orderType: '',
          adId: '12345'
        },
        vastmirroredadsJson: {
          VAST: {
            _version: '2.0',
            Ad: [
              {
                InLine: {
                  AdSystem: {
                    _text: 'TVN'
                  },
                  AdTitle: {
                    _text: 'TVN Video Ad'
                  },
                  Creatives: {
                    Creative: [
                      {
                        Linear: {
                          Duration: {
                            _text: '00:00:15'
                          },
                          MediaFiles: {
                            MediaFile: [
                              {
                                _attributes: {
                                  delivery: 'progressive',
                                  height: '404',
                                  type: 'video/mp4',
                                  width: '720'
                                },
                                _cdata:
                                  'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4'
                              }
                            ]
                          },
                          Mezzanine: {
                            _cdata:
                              'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218.mov'
                          },
                          TrackingEvents: {
                            Tracking: [
                              {
                                _attributes: {
                                  event: 'start'
                                },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: {
                                  event: 'firstQuartile'
                                },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: {
                                  event: 'midpoint'
                                },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: {
                                  event: 'thirdQuartile'
                                },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              },
                              {
                                _attributes: {
                                  event: 'complete'
                                },
                                _cdata:
                                  'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                              }
                            ]
                          },
                          VideoClicks: {
                            ClickThrough: {
                              _attributes: {
                                id: ''
                              },
                              _cdata:
                                'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15/url=https://www.biedronka.pl/pl/ale-tydzien'
                            }
                          }
                        },
                        UniversalAdId: {
                          _attributes: {
                            gdpr_consent: '',
                            idRegistry: 'TVN_Registry',
                            idValue: 'r205686.mov'
                          },
                          _text: 'r205686.mov'
                        },
                        _attributes: {
                          id: 'r205686.mov'
                        }
                      },
                      {
                        CompanionAds: {}
                      }
                    ]
                  },
                  Impression: [
                    {
                      _attributes: {
                        id: ''
                      },
                      _cdata:
                        'https://tvn.adocean.pl/event/nc=0/code=hbVN8PWXPCC8FMXjfznFJqp.0Z.AA_yZ6mO2Aww1lwP.B7/eprog=8/data=http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15/extra=;%2Fcr_info%3Dt_00%3A00%3A15%2Ccr_id%3Dr205686.mov%2Ccsnt%3D0%2Ckl%2Caopos_0%2Crealpos__POSITION_%2Cimp_2%2C%2Cproto_https'
                    },
                    {
                      _attributes: {
                        id: ''
                      }
                    },
                    {
                      _attributes: {
                        id: ''
                      }
                    }
                  ],
                  Error: [],
                  Extensions: {
                    Extension: []
                  }
                },
                _attributes: {
                  breakId: '123',
                  breakType: 'mirrored',
                  campaignId: 'CA_37178,OR_4,CR_15',
                  gdpr_consent: '',
                  id: 'Preroll',
                  sequence: 1,
                  timeOffset: '2020-09-29T22:30:05+02:00',
                  uid: ''
                }
              }
            ]
          }
        }
      }
    ],
    duration: 15,
    channel: 'TTV',
    version: 'v1_0_0',
    id: '04',
    time: '2020-09-29T22:30:05+02:00'
  };

  const headers: RequestHeaders = {
    'x-device-user-agent': 'test-deviceuseragentAGENT',
    'x-device-ip': 'test-deviceip',
    'x-device-referrer': 'test-devicereferrer'
  };

  test('is a function', () => {
    expect(typeof injectReqParams).toEqual('function');
  });

  test('added uid param', () => {
    expect(
      injectReqParams(
        scenario4,
        new RequestMacroParams(
          'test-uid',
          Protocol.https,
          '0',
          'paramName%3Dvalue',
          'TVN',
          '1',
          'test-gdpr',
          'test-rand8'
        ),
        headers
      )
    ).toEqual(uid);
  });

  test('empty uid by undefined', () => {
    expect(
      injectReqParams(
        scenario4,
        new RequestMacroParams(
          undefined,
          Protocol.https,
          '0',
          'paramName%3Dvalue',
          'TVN',
          '1',
          'test-gdpr',
          'test-rand8'
        ),
        headers
      )
    ).toEqual(emptyUid);
  });

  test('empty uid by empty string', () => {
    expect(
      injectReqParams(
        scenario4,
        new RequestMacroParams(
          '',
          Protocol.https,
          '0',
          'paramName%3Dvalue',
          'TVN',
          '1',
          'test-gdpr',
          'test-rand8'
        ),
        headers
      )
    ).toEqual(emptyUid);
  });

  test('empty all params by undefined', () => {
    expect(
      injectReqParams(
        reqParams01 as unknown as IConfiguration,
        new RequestMacroParams(
          undefined,
          Protocol.https,
          undefined,
          'paramName%3Dvalue',
          'TVN'
        ),
        headers
      )
    ).toEqual(allEmptyParams);
  });

  test('empty all params by empty string', () => {
    expect(
      injectReqParams(
        reqParams01 as unknown as IConfiguration,
        new RequestMacroParams('', Protocol.https, '0', '', '', '', '', ''),
        headers
      )
    ).toEqual(allEmptyParams);
  });

  test('empty all params by mixed empty params', () => {
    expect(
      injectReqParams(
        reqParams01 as unknown as IConfiguration,
        new RequestMacroParams(
          '',
          Protocol.https,
          undefined,
          'paramName%3Dvalue',
          'TVN',
          undefined,
          undefined,
          ''
        ),
        { 'x-device-user-agent': '', 'x-device-ip': undefined, 'x-device-referrer': undefined }
      )
    ).toEqual(allEmptyParams);
  });
});
