import { findAppropriateAd } from './findAppropriateAd';
import { multipleAdsVast } from '../../assets/mocks/multipleAdsVast';
import { dur15AdVast } from '../../assets/mocks/dur15AdVast';
import { dur30AdVast } from '../../assets/mocks/dur30AdVast';
import { oneAdVast } from '../../assets/mocks/oneAdVast';

describe('findAppropriateAd script test suite', () => {
  test('findAppropriateAd is function', () => {
    expect(typeof findAppropriateAd).toEqual('function');
  });

  test('matching ad from list, first in array', () => {
    const matchingAd = findAppropriateAd(multipleAdsVast.VAST.Ad, 15);
    expect(matchingAd[0]).toEqual(dur15AdVast);
  });

  test('matching ad from list, second in array', () => {
    const matchingAd = findAppropriateAd(multipleAdsVast.VAST.Ad, 30);
    expect(matchingAd[0]).toEqual(dur30AdVast);
  });

  test('matching ad from list; not found matching by duration 1', () => {
    const matchingAd = findAppropriateAd(multipleAdsVast.VAST.Ad, 10);
    expect(matchingAd).toEqual([]);
  });

  test('matching ad from list; not found matching by duration 2', () => {
    const matchingAd = findAppropriateAd(multipleAdsVast.VAST.Ad, 151);
    expect(matchingAd).toEqual([]);
  });

  test('matching ad from object, first in array', () => {
    const matchingAd = findAppropriateAd(oneAdVast.VAST.Ad, 15);
    expect(matchingAd[0]).toEqual(dur15AdVast);
  });

  test('matching ad from object; not found matching by duration', () => {
    const matchingAd = findAppropriateAd(oneAdVast.VAST.Ad, 10);
    expect(matchingAd).toEqual([]);
  });
});
