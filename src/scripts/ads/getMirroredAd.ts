import { IAd, AdVast4Normalized } from 'adpod-tools';

export const getMirroredAd = (
  bid: string,
  adData: IAd,
  time?: string,
  debugMode?: boolean
): AdVast4Normalized | null => {
  const [matchingAd] = adData.vastmirroredadsJson?.VAST.Ad ?? [];

  if (!matchingAd) {
    return null;
  }

  matchingAd._attributes.breakId = adData.metadata?.breakId || bid;
  matchingAd._attributes.linear = 'true';
  matchingAd._attributes.sequence = adData.position;

  if (time) {
    matchingAd._attributes.timeOffset = time;
  }

  // add <Debug> tag to mirrored VAST. It contains ad server URL request.
  // it's required to display ad server URL (also) once ad server returns empty vast or HTTP err
  if (debugMode && matchingAd?.InLine) {
    matchingAd.InLine.Debug = {
      AdRequestConnector: {
        _text: adData.connector || ''
      },
      Metadata: {
        _text: JSON.stringify(adData.metadata)
      }
    };
  }

  return matchingAd;
};
