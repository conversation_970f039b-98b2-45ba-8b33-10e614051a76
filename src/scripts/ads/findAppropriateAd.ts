import { LogLevel, hmsToMilliseconds, AdVast4Normalized } from 'adpod-tools';
import logger from '../../libs/logging/logger';

/**
 * Compare given ad duration with required duration in milliseconds.
 *
 * Returns:
 * True if ad has required duration
 * False if not found appropriate ad (selecting by duration), or Invalid advert duration
 *
 * @param adData ad data from VAST response
 * @param requiredDuration required ad duration in milliseconds
 * @returns boolean indicating if ad has required duration
 */
const compareAdDurations = (
  adData: AdVast4Normalized,
  requiredDurationMs: number
): boolean => {
  const adDurationHMS = adData.InLine?.Creatives.Creative[0].Linear?.Duration?._text;

  return !!adDurationHMS && requiredDurationMs === hmsToMilliseconds(adDurationHMS);
};

/**
 * Finds first advert matching given required duration in milliseconds from the given ads list
 *
 * @param ads list of ads
 * @param requiredDuration required ad duration in milliseconds
 * @returns first found advert matching to given required duration or null
 */
export const findAppropriateAd = (
  ads: AdVast4Normalized[] | undefined,
  linearAdDuration: number
): AdVast4Normalized[] => {
  const requiredDurationMs = linearAdDuration * 1000;

  logger('START_MATCH_ADSERVER_AD', { linearAdDuration }, LogLevel.dev);
  const matchingAd = ads?.find((adData) => compareAdDurations(adData, requiredDurationMs));

  if (matchingAd) {
    const { id, sequence } = matchingAd._attributes;
    logger('SUCCESS_MATCH_ADSERVER_AD', { linearAdDuration, id, sequence }, LogLevel.dev);
    return [matchingAd];
  }

  logger('CANNOT_MATCH_ADSERVER_AD', { linearAdDuration }, LogLevel.dev);
  return [];
};
