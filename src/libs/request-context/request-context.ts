import { AsyncLocalStorage } from 'async_hooks';
import { LoggerSettingsType } from '../../models/workerConfig';
import { availableItalyChannels, Channel } from 'adpod-tools';

export type RequestContext = {
  version?: string;
  channel?: string | availableItalyChannels | Channel;
  loggerSettings?: LoggerSettingsType;
};

export const requestContextStorage = new AsyncLocalStorage<RequestContext>();
