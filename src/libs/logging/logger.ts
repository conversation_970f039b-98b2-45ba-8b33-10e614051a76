import { LogDataType, LogLevel, logger } from 'adpod-tools';
import { validators } from '../../EnvValidation/envalidConfig';
import { requestContextStorage } from '../request-context';

const loggerService = (
  message: string,
  logData?: LogDataType,
  logLevel = LogLevel.info
): void => {
  const isLocal = validators.NODE_ENV === 'local';
  const loggerSettings = globalThis.workerConfig?.loggingGroups;
  const versionLogging = globalThis.workerConfig?.versionLogging;

  if (versionLogging?.length) {
    const store = requestContextStorage.getStore();
    if (
      store?.version &&
      store?.channel &&
      !!versionLogging.find((v) => v.channel === store.channel && v.version === store.version)
    ) {
      logger(message, logData, logLevel);
    }
    return;
  }

  if (!isLocal && loggerSettings?.[logLevel] !== 'on') {
    return;
  }

  logger(message, logData, logLevel);
};

export default loggerService;
