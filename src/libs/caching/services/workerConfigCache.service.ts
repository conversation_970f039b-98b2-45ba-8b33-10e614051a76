import { Injectable } from '@nestjs/common';
import { WorkerConfigType } from '../../../models/workerConfig';
import { ICacheProvider } from '../cache.provider';
import { validators } from '../../../EnvValidation/envalidConfig';
import { LogLevel } from 'adpod-tools';
import logger from '../../logging/logger';
import dayjs from 'dayjs';
import { TimeRange } from '../../valueObjects';
import { AWSS3FileContent } from 'adpod-aws';

@Injectable()
export class WorkerConfigCacheService {
  constructor(
    private readonly cacheManager: ICacheProvider,
    private readonly awsS3FileContent: AWSS3FileContent
  ) {}

  public async getWorkerConfig(): Promise<WorkerConfigType | undefined> {
    let workerConfig = await this.cacheManager.get<WorkerConfigType>('workerConfig');

    if (!workerConfig) {
      workerConfig = (await this.awsS3FileContent.getFileContent(
        validators.S3_WORKER_CONFIG_PATH
      )) as unknown as WorkerConfigType;
    }

    return workerConfig;
  }

  public async getScheduleConfigsAvailabilityTimeRange() {
    const { last, next } = await this.getScheduleConfigsAvailability();

    const now = dayjs();
    const lastDate = now.subtract(last, 'hour');
    const nextDate = now.add(next, 'hour');

    const timeRange = TimeRange.create(lastDate, nextDate);
    logger(
      'WORKER_CONFIG_SCHEDULE_CONFIGS_AVAILABILITY_TIME_RANGE',
      { scheduleConfigsAvailabilityTimeRange: timeRange.toFormat() },
      LogLevel.cache
    );

    return timeRange;
  }

  public async getCacheInterval() {
    const workerConfig = await this.getWorkerConfig();

    if (!workerConfig?.cacheInterval) {
      logger('WORKER_CONFIG_CACHE_INTERVAL_NOT_FOUND', undefined, LogLevel.error);
      throw new Error('Worker config or cache interval not found');
    }

    logger(
      'WORKER_CONFIG_CACHE_INTERVAL',
      { cacheInterval: workerConfig.cacheInterval },
      LogLevel.cache
    );

    return workerConfig.cacheInterval;
  }

  public async getScheduleConfigsAvailability() {
    const workerConfig = await this.getWorkerConfig();

    if (!workerConfig?.scheduleConfigsAvailability) {
      logger(
        'WORKER_CONFIG_SCHEDULE_CONFIGS_AVAILABILITY_NOT_FOUND',
        undefined,
        LogLevel.error
      );
      throw new Error('Worker config or schedule configs availability not found');
    }

    logger(
      'WORKER_CONFIG_SCHEDULE_CONFIGS_AVAILABILITY',
      { scheduleConfigsAvailability: workerConfig.scheduleConfigsAvailability },
      LogLevel.cache
    );

    return workerConfig.scheduleConfigsAvailability;
  }
}

//   const workerConfig: WorkerConfigType = {
//     responseIAB: {
//       include: {
//         explicit: [
//           {
//             channel: Channel.ttv,
//             version: 'nowtilus_v1_0_0_PROD'
//           }
//         ],
//         combination: {
//           channel: [Channel.tvn, Channel.mtit],
//           version: ['v1_0_0', 'v1_1_0']
//         }
//       },
//       exclude: {
//         explicit: [
//           {
//             channel: Channel.tvn,
//             version: 'v1_0_0'
//           }
//         ],
//         combination: {
//           channel: [Channel.ttv],
//           version: ['v1_0_0']
//         }
//       }
//     },
//     versionLogging: [],
//     loggingGroups: {} as any
//   };
