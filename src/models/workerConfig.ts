import { availableItalyChannels, Channel, LogLevel } from 'adpod-tools';

export type FreeWheelConfiguration = {
  filler: boolean;
};

export type FreeWheel = Record<string, FreeWheelConfiguration>;

export type ScheduleConfigsAvailability = {
  last: number;
  next: number;
  keepOutOfRangeConfigs: boolean;
  outOfRangeConfigsTTL: number;
};

export type EnabledConfig = {
  channel: Channel | availableItalyChannels;
  version: string;
};

export type Snowflake = {
  ipLogging: boolean;
  snowFlakeEnabledConfigs: EnabledConfig[];
};

export type CacheInterval = {
  chunkSize: number;
  delayInSeconds: number;
};

export type AnyCombination = {
  channel: EnabledConfig['channel'][];
  version: EnabledConfig['version'][];
};

export type CombinationConfigType = {
  explicit?: EnabledConfig[];
  combination?: AnyCombination;
};

export type ResponseIAB = {
  include?: CombinationConfigType;
  exclude?: CombinationConfigType;
};

export type LoggerSettingsType = Record<LogLevel, 'on' | 'off'>;

export type WorkerConfigType = {
  loggingGroups: LoggerSettingsType;
  playlistSingleTimeThreshold?: number;
  freeWheelDuration?: FreeWheel;
  programmaticEnchancedVastPlversions?: string[];
  snowflake?: Snowflake;
  responseIAB?: ResponseIAB;
  scheduleConfigsAvailability?: ScheduleConfigsAvailability;
  versionLogging: { channel: string; version: string }[];
  cacheInterval?: CacheInterval;
  audit: {
    modifiedDate: string;
    generatedBy: string;
  };
};
